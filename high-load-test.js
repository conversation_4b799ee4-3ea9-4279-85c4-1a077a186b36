#!/usr/bin/env node

/**
 * High load test to simulate the CPU usage issue after heavy events
 * This test creates many connections, sends lots of messages, then monitors
 * CPU and memory usage after the load subsides
 */

const WebSocket = require('ws')
const memoryMonitor = require('./src/MemoryMonitor.js')

class HighLoadTest {
    constructor() {
        this.serverUrl = 'ws://localhost:8080' // Adjust port as needed
        this.connections = []
        this.messagesSent = 0
        this.isRunning = false
        this.testPhase = 'idle'
    }
    
    async runTest() {
        console.log('🚀 Starting High Load Test...')
        
        // Phase 1: Create many connections
        await this.createConnections(50)
        
        // Phase 2: Send burst of messages
        await this.sendMessageBurst(1000)
        
        // Phase 3: Monitor after load subsides
        await this.monitorAfterLoad()
        
        // Phase 4: Cleanup
        await this.cleanup()
        
        console.log('✅ High Load Test Complete')
    }
    
    async createConnections(count) {
        console.log(`📡 Creating ${count} connections...`)
        this.testPhase = 'connecting'
        
        const promises = []
        for (let i = 0; i < count; i++) {
            promises.push(this.createConnection(i))
        }
        
        await Promise.all(promises)
        console.log(`✅ Created ${this.connections.length} connections`)
    }
    
    createConnection(id) {
        return new Promise((resolve, reject) => {
            try {
                const ws = new WebSocket(this.serverUrl)
                
                ws.on('open', () => {
                    // Send hi message to join
                    ws.send(JSON.stringify([{
                        m: 'hi',
                        token: null
                    }]))
                    
                    this.connections.push({ id, ws })
                    resolve()
                })
                
                ws.on('error', (error) => {
                    console.warn(`Connection ${id} failed:`, error.message)
                    resolve() // Don't fail the whole test
                })
                
                ws.on('close', () => {
                    // Remove from connections array
                    const index = this.connections.findIndex(conn => conn.id === id)
                    if (index !== -1) {
                        this.connections.splice(index, 1)
                    }
                })
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        ws.close()
                        resolve()
                    }
                }, 5000)
                
            } catch (error) {
                console.warn(`Failed to create connection ${id}:`, error.message)
                resolve()
            }
        })
    }
    
    async sendMessageBurst(messageCount) {
        console.log(`💥 Sending ${messageCount} messages...`)
        this.testPhase = 'high_load'
        
        const startTime = Date.now()
        const messagesPerConnection = Math.ceil(messageCount / this.connections.length)
        
        for (let i = 0; i < messageCount; i++) {
            const connection = this.connections[i % this.connections.length]
            if (connection && connection.ws.readyState === WebSocket.OPEN) {
                
                // Send different types of messages
                const messageType = i % 4
                let message
                
                switch (messageType) {
                    case 0: // Chat message
                        message = [{
                            m: 'a',
                            message: `Test message ${i} from connection ${connection.id}`
                        }]
                        break
                    case 1: // Cursor movement
                        message = [{
                            m: 'm',
                            x: Math.random() * 100,
                            y: Math.random() * 100
                        }]
                        break
                    case 2: // Note play
                        message = [{
                            m: 'n',
                            t: Date.now(),
                            n: [{
                                n: 'a' + Math.floor(Math.random() * 7),
                                v: Math.random(),
                                d: 0
                            }]
                        }]
                        break
                    case 3: // Channel join
                        message = [{
                            m: 'ch',
                            _id: `test-room-${Math.floor(Math.random() * 10)}`
                        }]
                        break
                }
                
                try {
                    connection.ws.send(JSON.stringify(message))
                    this.messagesSent++
                } catch (error) {
                    // Connection might be closed, ignore
                }
            }
            
            // Small delay to prevent overwhelming
            if (i % 100 === 0) {
                await new Promise(resolve => setTimeout(resolve, 10))
            }
        }
        
        const duration = Date.now() - startTime
        console.log(`✅ Sent ${this.messagesSent} messages in ${duration}ms`)
        console.log(`📊 Rate: ${Math.round(this.messagesSent / (duration / 1000))} messages/second`)
    }
    
    async monitorAfterLoad() {
        console.log('📈 Monitoring CPU and memory after load subsides...')
        this.testPhase = 'monitoring'
        
        // Stop sending messages and monitor for 60 seconds
        const monitorDuration = 60000
        const checkInterval = 5000
        const startTime = Date.now()
        
        while (Date.now() - startTime < monitorDuration) {
            const metrics = memoryMonitor.getMetrics()
            const cpuUsage = process.cpuUsage()
            
            console.log(`⏱️  ${Math.round((Date.now() - startTime) / 1000)}s - Memory: ${metrics.currentMemory.heapUsed}MB, Connections: ${metrics.currentConnections}`)
            console.log(`   Queue sizes: avg ${Math.round(metrics.averageQueueSize)}, monitoring: ${metrics.isMonitoring}`)
            
            await new Promise(resolve => setTimeout(resolve, checkInterval))
        }
        
        console.log('✅ Monitoring complete')
    }
    
    async cleanup() {
        console.log('🧹 Cleaning up connections...')
        this.testPhase = 'cleanup'
        
        // Close all connections
        for (const connection of this.connections) {
            if (connection.ws.readyState === WebSocket.OPEN) {
                connection.ws.close()
            }
        }
        
        // Wait for cleanup
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        console.log('✅ Cleanup complete')
    }
    
    getStatus() {
        return {
            phase: this.testPhase,
            connections: this.connections.length,
            messagesSent: this.messagesSent,
            isRunning: this.isRunning
        }
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    const test = new HighLoadTest()
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\n🛑 Received SIGINT, cleaning up...')
        await test.cleanup()
        process.exit(0)
    })
    
    test.runTest().catch(error => {
        console.error('❌ Test failed:', error)
        process.exit(1)
    })
}

module.exports = HighLoadTest
