const express = require("express");
const router = express.Router();
const { URLSearchParams } = require("url");
const axios = require("axios");

const BASE_URL = "https://beta-mpp.csys64.com";

router.get("/", (req, res) => {
    res.redirect("/");
});

router.get("/discord", (req, res) => {
    const params = new URLSearchParams();
    params.append("client_id", process.env.DISCORD_CLIENT_ID);
    params.append("callback_uri", `${BASE_URL}/oauth/discord/callback`);
    params.append("response_type", "code");
    params.append("scope", "identify");

    res.redirect(`https://discord.com/api/oauth2/authorize?${params.toString()}`);
});

router.get("/discord/callback", async (req, res) => {
    const data = new URLSearchParams();
    data.append("grant_type", "authorization_code");
    data.append("code", req.query.code);

    try {
        const token = await axios.post("https://discord.com/api/oauth2/token", data, {
            headers: {
                authorization: `Basic ${btoa(`${process.env.DISCORD_CLIENT_ID}:${process.env.DISCORD_CLIENT_TOKEN}`)}`,
            }
        });

        const user = await axios.get("https://discord.com/api/users/@me", {
            headers: {
                authorization: `${token.data.token_type} ${token.data.access_token}`
            }
        });

        res.send(user.data);
    } catch (error) {
        if (error.response) {
            res.status(error.response.status).send(error.response);
        } else {
            res.status(500).send(`Internal Server Error:<br>${error}`);
        }
    }
});

module.exports = router;

function btoa(str) {
    return Buffer.from(str).toString("base64");
}

