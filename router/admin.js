const express = require("express")
const router = express.Router()
const path = require("path")

router.get("/", (req, res) => {
    if (req.session && req.session.username === "admin") {
        res.sendFile(path.join(__dirname, "../pages/admin.html"))
    } else {
        res.redirect("/admin/login")
    }
})

router.get("/login", (req, res) => {
    if (req.session && req.session.username === "admin") {
        res.redirect("/admin")
    } else {
        res.type("text/html").send(
            `<form method="POST" action="/admin/login">
           <div>username<input type="text" name="username"></div>
           <div>password<input type="password" name="password"></div>
           <div><input type="submit" name="login"></div>
         </form>`
        )
    }
})

router.post("/api/tags/add", (req, res) => {
    const _id = req.body._id
    const tag = req.body.tag
    if (req.session && req.session.username === "admin") {
        const user = sql.prepare("SELECT * FROM users WHERE _id = ?;").get(_id)
        if (user) {
            const tags = user.tag ? user.tag.split(",") : []
            if (!tags.includes(tag)) {
                tags.push(tag)
                sql.prepare("UPDATE users SET tag = ? WHERE _id = ?;").run(
                    tags.join(","),
                    _id
                )
                res.json({ success: true })
            } else {
                res.json({ success: false, error: "User already has that tag" })
            }
        } else {
            res.json({ success: false, error: "User not found" })
        }
    } else {
        res.json({ success: false, error: "Not logged in" })
    }
})

router.post("/login", (req, res) => {
    const username = req.body.username
    const password = req.body.password
    if (username === "admin" && password === "nywg98eygo8weodieufew8wdef") {
        req.session.regenerate((err) => {
            req.session.username = "admin"
            res.redirect("/admin")
        })
    } else {
        res.redirect("/admin/login")
    }
})

module.exports = router
