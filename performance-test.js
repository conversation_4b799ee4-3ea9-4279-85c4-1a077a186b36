#!/usr/bin/env node

/**
 * Performance test script to verify optimizations
 * Run with: NODE_ENV=production node performance-test.js
 */

const fs = require('fs')
const path = require('path')

// Test 1: File I/O Performance
function testFileIO() {
    console.log('Testing File I/O Performance...')
    
    const testData = { test: 'data', timestamp: Date.now() }
    const iterations = 1000
    
    // Test synchronous writes (old way)
    console.time('Sync File Writes')
    for (let i = 0; i < iterations; i++) {
        fs.writeFileSync('./test-sync.json', JSON.stringify(testData))
    }
    console.timeEnd('Sync File Writes')
    
    // Test asynchronous writes (new way)
    console.time('Async File Writes')
    let completed = 0
    const startTime = Date.now()
    
    for (let i = 0; i < iterations; i++) {
        fs.writeFile('./test-async.json', JSON.stringify(testData), (err) => {
            if (err) console.error('Write error:', err)
            completed++
            if (completed === iterations) {
                console.timeEnd('Async File Writes')
                console.log(`Async writes completed in ${Date.now() - startTime}ms`)
                cleanup()
            }
        })
    }
}

// Test 2: Logging Performance
function testLogging() {
    console.log('\nTesting Logging Performance...')
    
    const logger = require('./src/Logger.js')
    const iterations = 10000
    
    // Test with production environment (should be fast)
    process.env.NODE_ENV = 'production'
    console.time('Production Logging')
    for (let i = 0; i < iterations; i++) {
        logger.debug('Test message', { data: i })
        logger.debugWebSocket('WebSocket message', { id: i })
        logger.debugMessage('Message', { content: 'test' })
    }
    console.timeEnd('Production Logging')
    
    // Test with development environment
    process.env.NODE_ENV = 'development'
    console.time('Development Logging')
    for (let i = 0; i < iterations; i++) {
        logger.debug('Test message', { data: i })
        logger.debugWebSocket('WebSocket message', { id: i })
        logger.debugMessage('Message', { content: 'test' })
    }
    console.timeEnd('Development Logging')
}

// Test 3: JSON Serialization Performance
function testJSONSerialization() {
    console.log('\nTesting JSON Serialization Performance...')
    
    const testObject = {
        id: 'test-id',
        message: 'This is a test message',
        timestamp: Date.now(),
        data: {
            nested: true,
            array: [1, 2, 3, 4, 5],
            complex: {
                more: 'data',
                numbers: [10, 20, 30]
            }
        }
    }
    
    const iterations = 100000
    
    // Test regular JSON.stringify
    console.time('JSON.stringify')
    for (let i = 0; i < iterations; i++) {
        JSON.stringify(testObject)
    }
    console.timeEnd('JSON.stringify')
    
    // Simulate the old expensive colorStringify (just for comparison)
    console.time('JSON.stringify with formatting')
    for (let i = 0; i < iterations; i++) {
        JSON.stringify(testObject, null, 2)
    }
    console.timeEnd('JSON.stringify with formatting')
}

// Test 4: Memory Usage
function testMemoryUsage() {
    console.log('\nMemory Usage Test...')
    
    const used = process.memoryUsage()
    console.log('Memory Usage:')
    for (let key in used) {
        console.log(`${key}: ${Math.round(used[key] / 1024 / 1024 * 100) / 100} MB`)
    }
}

function cleanup() {
    // Clean up test files
    try {
        fs.unlinkSync('./test-sync.json')
        fs.unlinkSync('./test-async.json')
    } catch (e) {
        // Files might not exist, ignore
    }
}

// Run all tests
function runTests() {
    console.log('=== MPP Server Performance Tests ===\n')
    
    testMemoryUsage()
    testJSONSerialization()
    testLogging()
    testFileIO()
    
    console.log('\n=== Performance Tests Complete ===')
    console.log('\nOptimizations Applied:')
    console.log('✓ Replaced synchronous file I/O with asynchronous')
    console.log('✓ Removed expensive JSON.colorStringify')
    console.log('✓ Implemented efficient logging system')
    console.log('✓ Disabled debug logging in production')
    console.log('✓ Optimized error handling')
}

// Handle cleanup on exit
process.on('exit', cleanup)
process.on('SIGINT', () => {
    cleanup()
    process.exit()
})

runTests()
