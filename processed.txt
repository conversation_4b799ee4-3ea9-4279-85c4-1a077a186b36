Statistical profiling result from isolate-0x43b18000-2447907-v8.log, (240410 ticks, 59 unaccounted, 0 excluded).

 [Shared libraries]:
   ticks  total  nonlib   name
  230877   96.0%          /usr/lib/libc.so.6
   4871    2.0%          /home/<USER>/.local/share/pnpm/nodejs/22.17.0/bin/node
      9    0.0%          [vdso]
      2    0.0%          /usr/lib/ld-linux-x86-64.so.2

 [JavaScript]:
   ticks  total  nonlib   name
   1719    0.7%   37.0%  JS: *listOnTimeout node:internal/timers:528:25
    459    0.2%    9.9%  JS: *<anonymous> /home/<USER>/extended-mppserver/src/Quota.js:91:37
    390    0.2%    8.4%  Builtin: ArrayPrototypeUnshift
    219    0.1%    4.7%  Builtin: KeyedLoadIC_Megamorphic
    167    0.1%    3.6%  Builtin: StoreIC
     96    0.0%    2.1%  JS: *percolateDown node:internal/priority_queue:45:16
     89    0.0%    1.9%  Builtin: CallFunction_ReceiverIsNotNullOrUndefined
     86    0.0%    1.8%  Builtin: RecordWriteSaveFP
     77    0.0%    1.7%  Builtin: CallApiCallbackOptimizedNoProfiling
     71    0.0%    1.5%  JS: *processTimers node:internal/timers:508:25
     70    0.0%    1.5%  Builtin: LoadIC
     58    0.0%    1.2%  Builtin: Call_ReceiverIsNotNullOrUndefined
     49    0.0%    1.1%  Builtin: CallFunction_ReceiverIsAny
     44    0.0%    0.9%  Builtin: GrowFastSmiOrObjectElements
     36    0.0%    0.8%  Builtin: AdaptorWithBuiltinExitFrame
     34    0.0%    0.7%  Builtin: BaselineOutOfLinePrologue
     33    0.0%    0.7%  Builtin: KeyedLoadIC_PolymorphicName
     28    0.0%    0.6%  Builtin: CEntry_Return1_ArgvOnStack_NoBuiltinExit
     28    0.0%    0.6%  Builtin: CEntry_Return1_ArgvOnStack_BuiltinExit
     26    0.0%    0.6%  Builtin: KeyedLoadIC
     26    0.0%    0.6%  Builtin: CallFunction_ReceiverIsNullOrUndefined
     21    0.0%    0.5%  Builtin: JSEntry
     20    0.0%    0.4%  Builtin: ArrayUnshift
     17    0.0%    0.4%  Builtin: CreateTypedArray
     16    0.0%    0.3%  JS: *startLoop /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:149:12
     15    0.0%    0.3%  Builtin: GetProperty
     13    0.0%    0.3%  JS: *clearBuffer node:internal/streams/writable:744:21
     13    0.0%    0.3%  Builtin: Call_ReceiverIsAny
     12    0.0%    0.3%  Builtin: KeyedStoreIC
     12    0.0%    0.3%  Builtin: JSEntryTrampoline
     11    0.0%    0.2%  Builtin: StrictEqual_Baseline
     10    0.0%    0.2%  JS: *Readable.push node:internal/streams/readable:387:35
      9    0.0%    0.2%  Builtin: StoreFastElementIC_InBounds
      9    0.0%    0.2%  Builtin: LoadIC_Megamorphic
      8    0.0%    0.2%  Builtin: Call_ReceiverIsNullOrUndefined_Baseline_Compact
      8    0.0%    0.2%  Builtin: Call_ReceiverIsNullOrUndefined
      7    0.0%    0.2%  JS: *emit node:events:465:44
      7    0.0%    0.2%  JS: *<anonymous> /home/<USER>/extended-mppserver/src/Client.js:158:31
      7    0.0%    0.2%  Builtin: CreateShallowObjectLiteral
      6    0.0%    0.1%  JS: *sendArray /home/<USER>/extended-mppserver/src/Room.js:247:14
      6    0.0%    0.1%  Builtin: ToBooleanForBaselineJump
      6    0.0%    0.1%  Builtin: DefineNamedOwnIC
      5    0.0%    0.1%  JS: *<anonymous> /home/<USER>/extended-mppserver/src/Room.js:645:22
      5    0.0%    0.1%  Builtin: ParseInt
      5    0.0%    0.1%  Builtin: MapIteratorPrototypeNext
      5    0.0%    0.1%  Builtin: LoadICTrampoline_Megamorphic
      5    0.0%    0.1%  Builtin: JSConstructStubGeneric
      5    0.0%    0.1%  Builtin: Construct
      5    0.0%    0.1%  Builtin: Call_ReceiverIsNotNullOrUndefined_Baseline_Compact
      5    0.0%    0.1%  Builtin: ArrayIncludesSmiOrObject
      4    0.0%    0.1%  JS: ^get node:internal/streams/readable:132:8
      4    0.0%    0.1%  JS: ^frame /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/sender.js:76:15
      4    0.0%    0.1%  JS: *writeOrBuffer node:internal/streams/writable:548:23
      4    0.0%    0.1%  JS: *spend /home/<USER>/extended-mppserver/src/Quota.js:126:10
      4    0.0%    0.1%  JS: *send /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/sender.js:346:7
      4    0.0%    0.1%  JS: *onwrite node:internal/streams/writable:615:17
      4    0.0%    0.1%  JS: *dataMessage /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:541:14
      4    0.0%    0.1%  Builtin: MapConstructor
      4    0.0%    0.1%  Builtin: LoadGlobalIC
      4    0.0%    0.1%  Builtin: Increment_Baseline
      4    0.0%    0.1%  Builtin: CallBoundFunction
      4    0.0%    0.1%  Builtin: CallApiCallbackGeneric
      4    0.0%    0.1%  Builtin: BitwiseAnd_Baseline
      4    0.0%    0.1%  Builtin: BaselineLeaveFrame
      4    0.0%    0.1%  Builtin: Add_Baseline
      3    0.0%    0.1%  JS: ^send /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/sender.js:346:7
      3    0.0%    0.1%  JS: ^_write node:internal/streams/writable:453:16
      3    0.0%    0.1%  JS: ^Socket.read node:net:780:33
      3    0.0%    0.1%  JS: ^Readable.read node:internal/streams/readable:647:35
      3    0.0%    0.1%  JS: ^<anonymous> /home/<USER>/extended-mppserver/src/Client.js:158:31
      3    0.0%    0.1%  JS: *get node:internal/bootstrap/node:474:8
      3    0.0%    0.1%  JS: *_unmask /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/buffer-util.js:58:17
      3    0.0%    0.1%  JS: *Writable.write node:internal/streams/writable:504:36
      3    0.0%    0.1%  JS: *Readable.read node:internal/streams/readable:647:35
      3    0.0%    0.1%  Builtin: Subtract_Baseline
      3    0.0%    0.1%  Builtin: ObjectPrototypeHasOwnProperty
      3    0.0%    0.1%  Builtin: NewRestArgumentsElements
      3    0.0%    0.1%  Builtin: LoadIC_NoFeedback
      3    0.0%    0.1%  Builtin: KeyedStoreIC_Megamorphic
      3    0.0%    0.1%  Builtin: JSBuiltinsConstructStub
      3    0.0%    0.1%  Builtin: InstanceOf_Baseline
      3    0.0%    0.1%  Builtin: CreateShallowArrayLiteral
      3    0.0%    0.1%  Builtin: ConstructFunction
      3    0.0%    0.1%  Builtin: BitwiseNot_Baseline
      2    0.0%    0.0%  JS: ^set node:internal/streams/writable:262:8
      2    0.0%    0.0%  JS: ^runNextTicks node:internal/process/task_queues:63:22
      2    0.0%    0.0%  JS: ^readableAddChunkPushByteMode node:internal/streams/readable:463:38
      2    0.0%    0.0%  JS: ^processTimers node:internal/timers:508:25
      2    0.0%    0.0%  JS: ^clearBuffer node:internal/streams/writable:744:21
      2    0.0%    0.0%  JS: *processTicksAndRejections node:internal/process/task_queues:72:35
      2    0.0%    0.0%  JS: *noop node:internal/util/debuglog:57:14
      2    0.0%    0.0%  JS: *consume /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:104:10
      2    0.0%    0.0%  JS: *allocUnsafe node:buffer:407:42
      2    0.0%    0.0%  JS: *_unrefTimer node:net:520:52
      2    0.0%    0.0%  JS: *<anonymous> node:internal/modules/cjs/loader:874:37
      2    0.0%    0.0%  Builtin: TypedArrayPrototypeToStringTag
      2    0.0%    0.0%  Builtin: ToString
      2    0.0%    0.0%  Builtin: StringEqual
      2    0.0%    0.0%  Builtin: StringAdd_CheckNone
      2    0.0%    0.0%  Builtin: StoreIC_NoFeedback
      2    0.0%    0.0%  Builtin: ObjectHasOwn
      2    0.0%    0.0%  Builtin: LoadIC_FunctionPrototype
      2    0.0%    0.0%  Builtin: LessThanOrEqual_Baseline
      2    0.0%    0.0%  Builtin: GreaterThan_Baseline
      2    0.0%    0.0%  Builtin: FunctionPrototypeHasInstance
      2    0.0%    0.0%  Builtin: ForInFilter
      2    0.0%    0.0%  Builtin: FindNonDefaultConstructorOrConstruct
      2    0.0%    0.0%  Builtin: FastNewFunctionContextFunction
      2    0.0%    0.0%  Builtin: Construct_Baseline
      2    0.0%    0.0%  Builtin: CallVarargs
      2    0.0%    0.0%  Builtin: CallIteratorWithFeedback
      2    0.0%    0.0%  Builtin: CallFunctionTemplate_Generic
      2    0.0%    0.0%  Builtin: BitwiseOr_Baseline
      2    0.0%    0.0%  Builtin: ArrayPrototypeShift
      1    0.0%    0.0%  JS: ~emitCloseNT node:internal/streams/destroy:133:21
      1    0.0%    0.0%  JS: ~Database /home/<USER>/extended-mppserver/node_modules/.pnpm/better-sqlite3@11.1.2/node_modules/better-sqlite3/lib/database.js:9:18
      1    0.0%    0.0%  JS: ~<anonymous> node:internal/crypto/hash:1:1
      1    0.0%    0.0%  JS: ~<anonymous> /home/<USER>/extended-mppserver/src/Message.js:162:16
      1    0.0%    0.0%  JS: ~<anonymous> /home/<USER>/extended-mppserver/node_modules/.pnpm/qs@6.14.0/node_modules/qs/lib/utils.js:8:26
      1    0.0%    0.0%  JS: ^writevGeneric node:internal/stream_base_commons:121:23
      1    0.0%    0.0%  JS: ^writeU_Int16BE node:internal/buffer:832:24
      1    0.0%    0.0%  JS: ^writeOrBuffer node:internal/streams/writable:548:23
      1    0.0%    0.0%  JS: ^tick /home/<USER>/extended-mppserver/src/Quota.js:114:9
      1    0.0%    0.0%  JS: ^stat node:internal/modules/cjs/loader:249:14
      1    0.0%    0.0%  JS: ^startLoop /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:149:12
      1    0.0%    0.0%  JS: ^sendFrame /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/sender.js:558:12
      1    0.0%    0.0%  JS: ^resolve node:path:1187:10
      1    0.0%    0.0%  JS: ^resetBuffer node:internal/streams/writable:364:21
      1    0.0%    0.0%  JS: ^processTicksAndRejections node:internal/process/task_queues:72:35
      1    0.0%    0.0%  JS: ^processPromiseRejections node:internal/process/promises:439:34
      1    0.0%    0.0%  JS: ^popAsyncContext node:internal/async_hooks:553:25
      1    0.0%    0.0%  JS: ^percolateDown node:internal/priority_queue:45:16
      1    0.0%    0.0%  JS: ^onStreamRead node:internal/stream_base_commons:166:22
      1    0.0%    0.0%  JS: ^normalizeString node:path:77:25
      1    0.0%    0.0%  JS: ^normalizeEncoding node:internal/util:229:27
      1    0.0%    0.0%  JS: ^next node:internal/per_context/primordials:331:9
      1    0.0%    0.0%  JS: ^listOnTimeout node:internal/timers:528:25
      1    0.0%    0.0%  JS: ^isTypedArray node:internal/util/types:9:22
      1    0.0%    0.0%  JS: ^isRelative node:internal/modules/cjs/loader:1978:20
      1    0.0%    0.0%  JS: ^isInt32 node:internal/validators:44:17
      1    0.0%    0.0%  JS: ^getInfo /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:185:10
      1    0.0%    0.0%  JS: ^getEncodingOps node:buffer:721:24
      1    0.0%    0.0%  JS: ^get node:internal/streams/duplex:161:8
      1    0.0%    0.0%  JS: ^get node:internal/bootstrap/node:474:8
      1    0.0%    0.0%  JS: ^emitBeforeScript node:internal/async_hooks:509:26
      1    0.0%    0.0%  JS: ^emit node:events:465:44
      1    0.0%    0.0%  JS: ^draw /home/<USER>/extended-mppserver/src/Room.js:512:9
      1    0.0%    0.0%  JS: ^deserializePackageJSON node:internal/modules/package_json_reader:42:32
      1    0.0%    0.0%  JS: ^dataMessage /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:541:14
      1    0.0%    0.0%  JS: ^consume /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:104:10
      1    0.0%    0.0%  JS: ^basename node:path:1414:11
      1    0.0%    0.0%  JS: ^alignPool node:buffer:164:19
      1    0.0%    0.0%  JS: ^afterWriteDispatched node:internal/stream_base_commons:154:30
      1    0.0%    0.0%  JS: ^addChunk node:internal/streams/readable:550:18
      1    0.0%    0.0%  JS: ^_write /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:89:9
      1    0.0%    0.0%  JS: ^Socket._writev node:net:972:36
      1    0.0%    0.0%  JS: ^Socket._writeGeneric node:net:935:42
      1    0.0%    0.0%  JS: ^SafeIterator node:internal/per_context/primordials:328:16
      1    0.0%    0.0%  JS: ^ReadableState node:internal/streams/readable:262:23
      1    0.0%    0.0%  JS: ^<anonymous> node:internal/validators:459:42
      1    0.0%    0.0%  JS: ^<anonymous> /home/<USER>/extended-mppserver/src/Message.js:97:16
      1    0.0%    0.0%  JS: ^<anonymous> /home/<USER>/extended-mppserver/src/Message.js:204:18
      1    0.0%    0.0%  JS: ^<anonymous> /home/<USER>/extended-mppserver/src/Message.js:162:16
      1    0.0%    0.0%  JS: *wrappedFn node:internal/errors:535:21
      1    0.0%    0.0%  JS: *socketOnData /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/websocket.js:1354:22
      1    0.0%    0.0%  JS: *setPosition node:internal/timers:427:21
      1    0.0%    0.0%  JS: *runNextTicks node:internal/process/task_queues:63:22
      1    0.0%    0.0%  JS: *resetPoints /home/<USER>/extended-mppserver/src/Quota.js:107:16
      1    0.0%    0.0%  JS: *nextTick node:internal/process/task_queues:113:18
      1    0.0%    0.0%  JS: *maybeReadMore_ node:internal/streams/readable:864:24
      1    0.0%    0.0%  JS: *isFileType node:fs:204:20
      1    0.0%    0.0%  JS: *getInfo /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:185:10
      1    0.0%    0.0%  JS: *get readyState /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/websocket.js:181:17
      1    0.0%    0.0%  JS: *get node:net:704:16
      1    0.0%    0.0%  JS: *get node:internal/streams/readable:132:8
      1    0.0%    0.0%  JS: *get node:internal/bootstrap/node:100:8
      1    0.0%    0.0%  JS: *frame /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/sender.js:76:15
      1    0.0%    0.0%  JS: *emitBeforeScript node:internal/async_hooks:509:26
      1    0.0%    0.0%  JS: *_write node:internal/streams/writable:453:16
      1    0.0%    0.0%  JS: *FastBuffer node:internal/buffer:960:14
      1    0.0%    0.0%  JS: *<anonymous> /home/<USER>/extended-mppserver/src/Message.js:97:16
      1    0.0%    0.0%  Builtin: TypedArrayPrototypeByteOffset
      1    0.0%    0.0%  Builtin: TypedArrayPrototypeBuffer
      1    0.0%    0.0%  Builtin: ToBoolean
      1    0.0%    0.0%  Builtin: StringSubstring
      1    0.0%    0.0%  Builtin: StringPrototypeEndsWith
      1    0.0%    0.0%  Builtin: StoreInArrayLiteralICBaseline
      1    0.0%    0.0%  Builtin: StoreInArrayLiteralIC
      1    0.0%    0.0%  Builtin: StoreICBaseline
      1    0.0%    0.0%  Builtin: RecordWriteIgnoreFP
      1    0.0%    0.0%  Builtin: ObjectSetPrototypeOf
      1    0.0%    0.0%  Builtin: NumberIsNaN
      1    0.0%    0.0%  Builtin: LoadIC_StringLength
      1    0.0%    0.0%  Builtin: LoadICBaseline
      1    0.0%    0.0%  Builtin: LessThan_Baseline
      1    0.0%    0.0%  Builtin: KeyedLoadICTrampoline_Megamorphic
      1    0.0%    0.0%  Builtin: KeyedLoadICBaseline
      1    0.0%    0.0%  Builtin: JsonStringify
      1    0.0%    0.0%  Builtin: InterpreterEntryTrampoline
      1    0.0%    0.0%  Builtin: GreaterThanOrEqual_Baseline
      1    0.0%    0.0%  Builtin: FindOrderedHashSetEntry
      1    0.0%    0.0%  Builtin: FindOrderedHashMapEntry
      1    0.0%    0.0%  Builtin: FastNewRestArguments
      1    0.0%    0.0%  Builtin: FastNewObject
      1    0.0%    0.0%  Builtin: DivideSmi_Baseline
      1    0.0%    0.0%  Builtin: Decrement_Baseline
      1    0.0%    0.0%  Builtin: CreateEmptyArrayLiteral
      1    0.0%    0.0%  Builtin: CopyDataProperties
      1    0.0%    0.0%  Builtin: CallWithArrayLike
      1    0.0%    0.0%  Builtin: BitwiseOrSmi_Baseline
      1    0.0%    0.0%  Builtin: ArrayShift
      1    0.0%    0.0%  Builtin: AddSmi_Baseline

 [C++]:
   ticks  total  nonlib   name
     72    0.0%    1.5%  fwrite@@GLIBC_2.2.5
     25    0.0%    0.5%  syscall@@GLIBC_2.2.5
     18    0.0%    0.4%  __libc_malloc@@GLIBC_2.2.5
     16    0.0%    0.3%  std::basic_ostream<char, std::char_traits<char> >& std::__ostream_insert<char, std::char_traits<char> >(std::basic_ostream<char, std::char_traits<char> >&, char const*, long)
     15    0.0%    0.3%  _IO_file_xsputn@@GLIBC_2.2.5
     11    0.0%    0.2%  std::ostream::sentry::sentry(std::ostream&)
     10    0.0%    0.2%  epoll_pwait@@GLIBC_2.6
      9    0.0%    0.2%  sigemptyset@@GLIBC_2.2.5
      9    0.0%    0.2%  sigaddset@@GLIBC_2.2.5
      8    0.0%    0.2%  __pthread_mutex_lock@GLIBC_2.2.5
      5    0.0%    0.1%  cfree@GLIBC_2.2.5
      5    0.0%    0.1%  __errno_location@@GLIBC_2.2.5
      4    0.0%    0.1%  isprint@@GLIBC_2.2.5
      4    0.0%    0.1%  __clock_gettime@@GLIBC_PRIVATE
      3    0.0%    0.1%  calloc@@GLIBC_2.2.5
      3    0.0%    0.1%  __madvise@@GLIBC_PRIVATE
      2    0.0%    0.0%  writev@@GLIBC_2.2.5
      2    0.0%    0.0%  std::ostream& std::ostream::_M_insert<long>(long)
      2    0.0%    0.0%  std::_Rb_tree_insert_and_rebalance(bool, std::_Rb_tree_node_base*, std::_Rb_tree_node_base*, std::_Rb_tree_node_base&)
      2    0.0%    0.0%  pthread_cond_signal@@GLIBC_2.3.2
      2    0.0%    0.0%  operator delete(void*)
      2    0.0%    0.0%  __pthread_mutex_unlock@GLIBC_2.2.5
      2    0.0%    0.0%  __munmap@@GLIBC_PRIVATE
      1    0.0%    0.0%  std::ostreambuf_iterator<char, std::char_traits<char> > std::num_put<char, std::ostreambuf_iterator<char, std::char_traits<char> > >::_M_insert_int<long>(std::ostreambuf_iterator<char, std::char_traits<char> >, std::ios_base&, char, long) const
      1    0.0%    0.0%  std::ostream::flush()
      1    0.0%    0.0%  std::_Rb_tree_decrement(std::_Rb_tree_node_base*)
      1    0.0%    0.0%  readlink@@GLIBC_2.2.5
      1    0.0%    0.0%  operator delete(void*, unsigned long)
      1    0.0%    0.0%  brk@@GLIBC_2.2.5
      1    0.0%    0.0%  __pthread_rwlock_wrlock@GLIBC_2.2.5
      1    0.0%    0.0%  _IO_do_write@@GLIBC_2.2.5

 [Summary]:
   ticks  total  nonlib   name
   4353    1.8%   93.6%  JavaScript
    239    0.1%    5.1%  C++
     66    0.0%    1.4%  GC
  235759   98.1%          Shared libraries
     59    0.0%          Unaccounted

 [C++ entry points]:
   ticks    cpp   total   name
     71   41.8%    0.0%  fwrite@@GLIBC_2.2.5
     23   13.5%    0.0%  syscall@@GLIBC_2.2.5
     15    8.8%    0.0%  std::basic_ostream<char, std::char_traits<char> >& std::__ostream_insert<char, std::char_traits<char> >(std::basic_ostream<char, std::char_traits<char> >&, char const*, long)
     15    8.8%    0.0%  _IO_file_xsputn@@GLIBC_2.2.5
     13    7.6%    0.0%  __libc_malloc@@GLIBC_2.2.5
     11    6.5%    0.0%  std::ostream::sentry::sentry(std::ostream&)
      4    2.4%    0.0%  isprint@@GLIBC_2.2.5
      3    1.8%    0.0%  cfree@GLIBC_2.2.5
      3    1.8%    0.0%  __madvise@@GLIBC_PRIVATE
      2    1.2%    0.0%  writev@@GLIBC_2.2.5
      2    1.2%    0.0%  std::ostream& std::ostream::_M_insert<long>(long)
      2    1.2%    0.0%  std::_Rb_tree_insert_and_rebalance(bool, std::_Rb_tree_node_base*, std::_Rb_tree_node_base*, std::_Rb_tree_node_base&)
      1    0.6%    0.0%  std::ostream::flush()
      1    0.6%    0.0%  std::_Rb_tree_decrement(std::_Rb_tree_node_base*)
      1    0.6%    0.0%  readlink@@GLIBC_2.2.5
      1    0.6%    0.0%  operator delete(void*)
      1    0.6%    0.0%  brk@@GLIBC_2.2.5
      1    0.6%    0.0%  __pthread_mutex_unlock@GLIBC_2.2.5

 [Bottom up (heavy) profile]:
  Note: percentage shows a share of a particular caller in the total
  amount of its parent calls.
  Callers occupying less than 1.0% are not shown.

   ticks parent  name
  230877   96.0%  /usr/lib/libc.so.6
  21742    9.4%    /home/<USER>/.local/share/pnpm/nodejs/22.17.0/bin/node
  21252   97.7%      JS: *<anonymous> /home/<USER>/extended-mppserver/src/Quota.js:91:37
  21234   99.9%        JS: *listOnTimeout node:internal/timers:528:25
  21234  100.0%          JS: *processTimers node:internal/timers:508:25
    307    1.4%      JS: *resetPoints /home/<USER>/extended-mppserver/src/Quota.js:107:16
    158   51.5%        JS: ^Quota /home/<USER>/extended-mppserver/src/Quota.js:43:16
    123   77.8%          JS: ^playNote /home/<USER>/extended-mppserver/src/Room.js:449:13
    123  100.0%            JS: ^<anonymous> /home/<USER>/extended-mppserver/src/Message.js:162:16
     35   22.2%          JS: ~playNote /home/<USER>/extended-mppserver/src/Room.js:449:13
     19   54.3%            JS: ^<anonymous> /home/<USER>/extended-mppserver/src/Message.js:162:16
     16   45.7%            JS: ~<anonymous> /home/<USER>/extended-mppserver/src/Message.js:162:16
    149   48.5%        JS: ^setParams /home/<USER>/extended-mppserver/src/Quota.js:78:14
    149  100.0%          JS: ^Quota /home/<USER>/extended-mppserver/src/Quota.js:43:16
    117   78.5%            JS: ^playNote /home/<USER>/extended-mppserver/src/Room.js:449:13
     32   21.5%            JS: ~playNote /home/<USER>/extended-mppserver/src/Room.js:449:13

   4871    2.0%  /home/<USER>/.local/share/pnpm/nodejs/22.17.0/bin/node
   2598   53.3%    /home/<USER>/.local/share/pnpm/nodejs/22.17.0/bin/node
   1602   61.7%      JS: *<anonymous> /home/<USER>/extended-mppserver/src/Quota.js:91:37
   1601   99.9%        JS: *listOnTimeout node:internal/timers:528:25
   1601  100.0%          JS: *processTimers node:internal/timers:508:25
    561   21.6%      JS: *listOnTimeout node:internal/timers:528:25
    559   99.6%        JS: *processTimers node:internal/timers:508:25
     70    2.7%      JS: *<anonymous> /home/<USER>/extended-mppserver/src/Client.js:158:31
     56   80.0%        JS: *receiverOnMessage /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/websocket.js:1219:27
     56  100.0%          JS: *dataMessage /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:541:14
     56  100.0%            JS: *startLoop /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/receiver.js:149:12
     14   20.0%        JS: *emit node:events:465:44
     14  100.0%          JS: ^receiverOnMessage /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/websocket.js:1219:27
     14  100.0%            JS: *emit node:events:465:44
     32    1.2%      JS: ^<anonymous> /home/<USER>/extended-mppserver/src/Client.js:158:31
     25   78.1%        JS: *emit node:events:465:44
     25  100.0%          JS: ^receiverOnMessage /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/websocket.js:1219:27
     25  100.0%            JS: *emit node:events:465:44
      7   21.9%        JS: ^emit node:events:465:44
      7  100.0%          JS: ^receiverOnMessage /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/websocket.js:1219:27
      7  100.0%            JS: ^emit node:events:465:44
     32    1.2%      JS: *clearBuffer node:internal/streams/writable:744:21
     25   78.1%        JS: *send /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/sender.js:346:7
     17   68.0%          JS: *sendArray /home/<USER>/extended-mppserver/src/Room.js:247:14
     11   64.7%            JS: ^draw /home/<USER>/extended-mppserver/src/Room.js:512:9
      6   35.3%            JS: ^setCords /home/<USER>/extended-mppserver/src/Room.js:366:13
      4   16.0%          JS: ^send /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/websocket.js:448:7
      4  100.0%            JS: ^sendArray /home/<USER>/extended-mppserver/src/Client.js:103:14
      4   16.0%          JS: *<anonymous> /home/<USER>/extended-mppserver/src/Room.js:645:22
      4  100.0%            JS: *<anonymous> /home/<USER>/extended-mppserver/src/Message.js:97:16
      7   21.9%        JS: ^Writable.uncork node:internal/streams/writable:520:37
      7  100.0%          JS: ^sendFrame /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/sender.js:558:12
      7  100.0%            JS: ^dispatch /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/sender.js:498:11
    474    9.7%    JS: *listOnTimeout node:internal/timers:528:25
    474  100.0%      JS: *processTimers node:internal/timers:508:25
     59    1.2%    JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
     55   93.2%      JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
     19   34.5%        /home/<USER>/.local/share/pnpm/nodejs/22.17.0/bin/node
     17   89.5%          JS: ~<anonymous> /home/<USER>/extended-mppserver/node_modules/.pnpm/axios@1.8.4/node_modules/axios/dist/node/axios.cjs:1:1
     17  100.0%            JS: ^<anonymous> node:internal/modules/cjs/loader:1680:37
      2   10.5%          JS: ~lib/web/fetch/response.js node:internal/deps/undici/undici:9047:30
      2  100.0%            JS: ^__require node:internal/deps/undici/undici:5:49
      4    7.3%        JS: ~<anonymous> node:https:1:1
      4  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      4  100.0%            JS: ~<anonymous> /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/websocket.js:1:1
      4    7.3%        JS: ~<anonymous> node:http2:1:1
      4  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      4  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      4    7.3%        JS: ~<anonymous> node:crypto:1:1
      4  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      4  100.0%            JS: ~<anonymous> /home/<USER>/extended-mppserver/node_modules/.pnpm/dotenv@16.4.7/node_modules/dotenv/lib/main.js:1:1
      3    5.5%        JS: ~<anonymous> node:tls:1:1
      3  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      3  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      3    5.5%        JS: ~<anonymous> node:internal/streams/duplex:1:1
      3  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      3  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      2    3.6%        JS: ~<anonymous> node:internal/http2/core:1:1
      2  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      2  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      2    3.6%        JS: ~<anonymous> node:internal/cluster/primary:1:1
      2  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      2  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:net:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:internal/webstreams/readablestream:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:internal/streams/readable:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:internal/perf/timerify:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:internal/http2/compat:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:internal/fs/promises:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:internal/crypto/hkdf:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:internal/child_process:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:cluster:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:assert:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^compileForPublicLoader node:internal/bootstrap/realm:332:25
      1    1.8%        JS: ~<anonymous> node:_tls_common:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:_http_server:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:_http_outgoing:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      1    1.8%        JS: ~<anonymous> node:_http_common:1:1
      1  100.0%          JS: ^compileForInternalLoader node:internal/bootstrap/realm:384:27
      1  100.0%            JS: ^requireBuiltin node:internal/bootstrap/realm:421:24
      2    3.4%      JS: ~<anonymous> /home/<USER>/extended-mppserver/node_modules/.pnpm/dotenv@16.4.7/node_modules/dotenv/lib/main.js:1:1
      2  100.0%        JS: ~<anonymous> /home/<USER>/extended-mppserver/index.js:1:1
      2  100.0%          JS: ~<anonymous> node:internal/main/run_main_module:1:1
      1    1.7%      JS: ~<anonymous> /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/lib/websocket.js:1:1
      1  100.0%        JS: ~<anonymous> /home/<USER>/extended-mppserver/node_modules/.pnpm/ws@8.18.1/node_modules/ws/index.js:1:1
      1  100.0%          JS: ~<anonymous> /home/<USER>/extended-mppserver/index.js:1:1
      1  100.0%            JS: ~<anonymous> node:internal/main/run_main_module:1:1
      1    1.7%      JS: ^compileForPublicLoader node:internal/bootstrap/realm:332:25
      1  100.0%        JS: ^loadBuiltinModule node:internal/modules/helpers:101:27
      1  100.0%          JS: ^loadBuiltinWithHooks node:internal/modules/cjs/loader:1160:30
      1  100.0%            JS: ^wrapModuleLoad node:internal/modules/cjs/loader:228:24

