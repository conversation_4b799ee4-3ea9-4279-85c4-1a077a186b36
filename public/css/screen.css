* { margin: 0; }
* { user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; }
html, body { width: 100%; height: 100%; overflow: hidden; font: 20pt verdana, "DejaVu Sans", sans-serif; color: white; text-shadow: #444 1px 1px; }
body { position: absolute; }
body {
background: #242464; /* Old browsers */
background: -moz-radial-gradient(center, ellipse cover,  #242464 0%, #000024 100%); /* FF3.6+ */
background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%,#242464), color-stop(100%,#000024)); /* Chrome,Safari4+ */
background: -webkit-radial-gradient(center, ellipse cover,  #242464 0%,#000024 100%); /* Chrome10+,Safari5.1+ */
background: -o-radial-gradient(center, ellipse cover,  #242464 0%,#000024 100%); /* Opera 12+ */
background: -ms-radial-gradient(center, ellipse cover,  #242464 0%,#000024 100%); /* IE10+ */
background: radial-gradient(ellipse at center,  #242464 0%,#000024 100%); /* W3C */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#242464', endColorstr='#000024',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
-webkit-transition: background 1000ms linear;
}

a { cursor: pointer; color: #f46; transition: color 0.25s; }
a:hover { color: #e05; transition: color 0.25s; }
.link { text-decoration: underline; cursor: pointer; color: #ffee00; }

table { border: 0; padding: 0; margin: 0; }

#names { position: fixed; top: 4px; left: 4px; font-size: 12px; }
#names .name { float: left; position: relative; padding: 4px; margin: 2px; 
	border-radius: 2px; -webkit-border-radius: 2px; -moz-border-radius: 2px; 
	min-width: 50px; text-align: center; cursor: pointer; line-height: 15px; }
#names .nametag {
	float: left;
	position: relative;
	pointer-events: none;
	padding: 4px;
	margin-right: 5px;
	border-radius: 2px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	text-align: center;
	cursor: default;
	font-size: 10px;
	line-height: 7px;
}
#names .name.me:after { content: "Me"; position: absolute; top: -4px; right: 50%; font-size: 10px; }
#names .name.owner:before { content: url('/img/crown.png'); position: absolute; top: -8px; left: 4px; }
#names .name.play { transform: translateY(-4px); -webkit-transform: translateY(-4px); }
#names .name.muted-notes { color: #f88; }
#names .name.muted-notes:after { content: "MUTE"; position: absolute; top: -4px; right: 50%; font-size: 8px; }
#names .name.muted-lines { color: #f88; }
#names .name.muted-lines:after { content: "MUTE"; position: absolute; top: -4px; right: 50%; font-size: 8px; }
#names .name.muted-chat { color: #f88; }
#names .name.muted-chat:after { content: "MUTE"; position: absolute; top: -4px; right: 50%; font-size: 8px; }

#piano { width: 95%; height: 20%; margin: auto; position: relative; overflow: hidden; padding-left: 1%; }
#piano .key { float: left; width: 1.8%; height: 90%; border: 1px solid black; background: #fff; 
	margin-left: -0.5%; margin-bottom: 100%;
	box-shadow: 1px 2px 5px #000; -webkit-box-shadow: 1px 2px 5px #000; -moz-box-shadow: 1px 2px 5px #000;
	border-radius: 2px; -webkit-border-radius: 2px; -moz-border-radius: 2px;
	transition: background 4s ease-out; -webkit-transition: background 4s ease-out; -moz-transition: background 4s ease-out;
	-o-transition: background 4s ease-out; padding: 0px;
	overflow: hidden; }
#piano .key.c, #piano .key.f { margin-left: 0; }
#piano .key.sharp { width: 1.2%; height: 50%; background: #000; margin-left: -0.9%;
	position: relative; }

#piano .key.loading { background: #888; }

#piano .key.play { transform: translateY(1%); -webkit-transform: translateY(1%); -webkit-box-shadow: 0px 1px 2px #000; }

.ease-out { transition: left 0.1s ease-out; -webkit-transition: left 0.1s ease-out; -moz-transition: left 0.1s ease-out;
	-o-transition: left 0.1s ease-out; }
.ease-in { transition: left 0.1s ease-in; -webkit-transition: left 0.1s ease-in; -moz-transition: left 0.1s ease-in;
	-o-transition: left 0.1s ease-in; }
.slide-left { left: -100%; }
.slide-right { left: 100%; }

.cursor { width: 23px; height: 30px; background: url('/img/cursor.png'); position: absolute; pointer-events: none;
	margin-left: -5px; margin-top: -5px; left: 200%; top: 100%;}
.cursor .name { display: inline; position: relative; left: 12px; top: 10px; pointer-events: none; color: #fff; background: #000;
	border-radius: 2px; -webkit-border-radius: 2px; -moz-border-radius: 2px; white-space:nowrap; padding: 1px; font-size: 10px; }
.cursor.owner .name:after { content: url('/img/crown.png'); position: relative; top: -8px; left: 0px; }
.cursor .name { display: inline-block; }
//.cursor .spotlight { display: none; position: relative; left: -14px; top: -9px; width: 32px; height: 32px;
//	border: 4px solid white; -webkit-border-radius: 100%; }

.participant-menu { display: none; position: fixed; background: black; width: 150px; font-size: 12px; padding: 0px; margin: 0px;
	border-radius: 2px; }
.participant-menu:last-child .menu-item:hover { border-bottom-left-radius: inherit; border-bottom-right-radius: inherit; }
.participant-menu .info { user-select: text; -webkit-user-select: text; -moz-user-select: text; -ms-user-select: text;
  height: 40px; text-align: center; line-height: 40px; font-size: 9px; color: rgba(255,255,255,0.9); overflow: hidden;
	opacity: 0; transition: opacity 0.2s ease-out; }
.participant-menu .info:hover { opacity: 1; transition: opacity 0.2s ease-out; }
.participant-menu .menu-item { cursor: pointer; margin: 0px; padding: 15px 5px;
	border-top: 1px solid rgba(255,255,255,0.3); }
.participant-menu .menu-item:hover { background: rgba(255,255,255,0.4); }
.participant-menu .menu-item.clicked { background: rgba(255,255,255,0.5); }

#crown { position: absolute; width: 16px; height: 16px; background: url('/img/crown.png') no-repeat; cursor: pointer;
	font-size: 10px; }
#crown span { margin-left: 16px; margin-top: 2px; }

#crownsolo-notice { position: fixed; top: 20%; width: 100%; text-align: center; opacity: 0.5; font-size: 20px; pointer-events: none; display: none; }

#bottom{ position: fixed; bottom: 0px; left: 0px; width: 100%; height: 60px; background: #00002410; margin-bottom: 3px; }

#room, #room * { cursor: pointer; }
#room { position: absolute; left: 0, top: 0; padding: 5px; width: 240px; height: 12px; background: #898; border: 1px solid #aba; cursor: pointer;
	margin: 4px 24px; font-size: 12px; }
#room .info { white-space: nowrap; line-height: 12px; overflow: hidden; height: 20px; }

#room .info.lobby { color: #eeffbb; }
#room .info.not-visible { color: #ddeeff; }
#room .info.crownsolo:after { content: url('/img/crownsolo.png'); position: relative; top: 2px; margin-left: 4px; }
#room .info.no-chat:after { content: url('/img/no-chat.png'); position: relative; top: 2px; margin-left: 4px; }

#room .expand { width: 24px; height: 100%; position: absolute; right: 0; top: 0; background: #aba url('/img/arrow.png') no-repeat center 0%; }
#room .more { display: none; position: absolute; bottom: 100%; left: -1px; width: 100%; overflow: hidden; overflow-y: scroll; background: #898;
	border: 1px solid #aba; max-height: 600px; }
#room .more > div { margin: 0; padding: 3px 6px 0px 6px; width: 100%; height: 20px; }
#room .more .info:hover { background: #aba; }
#room .more .new { background: #9a9; }
#room .more .new:hover { background: #cdc; }

.ugly-button { height: 12px; font-size: 12px; background: rgba(170, 187, 170, 0.35); border: 1px solid #898; padding: 5px; cursor: pointer; line-height: 12px;
	border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px; width: 100px; overflow: hidden; white-space:nowrap; }
.ugly-button:hover { background: rgba(187, 204, 170, 0.35) }
.ugly-button.stuck { background: rgba(204, 187, 170, 0.35); }
#new-room-btn { position: absolute; left: 300px; top: 4px; }
#play-alone-btn { position: absolute; left: 420px; top: 4px; }
#sound-btn { position: absolute; left: 540px; top: 4px; }
#room-settings-btn { position: absolute; left: 660px; top: 4px; display: none; }
#midi-btn { position: absolute; left: 300px; top: 32px; }
#none-btn { position: absolute; left: 420px; top: 32px; }
#synth-btn { position: absolute; left: 540px; top: 32px; }

#bottom>#relative>#buttons {
	text-align: center;
}

#tooltip { position: absolute; pointer-events: none; background: #000; color: #fff; font-size: 10px; }
.knob { cursor: pointer; }
.switched-on { background: #ff8; }

#status { position: absolute; left: 0px; bottom: 10px; width: 320px; height: 20px; padding: 5px; font-size: 20px; font-weight: 800;
	line-height: 20px; pointer-events: none; }
#status .number { font-size: 35px; }

#volume { position: absolute; right: 20px; top: 0px; width: 100px; height: 40px; margin: 10px; }
.volume-slider { width: 100%; height: 100%; background: url("/img/volume2.png") no-repeat; background-position: 50% 50%;
	-webkit-appearance: none; }

#volume-label { position: absolute; right: 30px; bottom: 10px; font-size: 10px; color: #ccc; }

#banner { width: 468px; height: 60px; position: absolute; right: 0; top: 0; font-size: 18px; display: none; }
#banner a { color: #ffdd00; }

#quota { width: 100%; height: 3px; position: fixed; bottom: 0; left: 0; background: #f80; }
#quota .value { width: 100%; height: 100%; display: block; background: #fd0; }

.relative { position: relative; width: 100%; height: 100%; }

.notification { position: absolute; }
.notification-body { background: #fea; border-color: #fea; padding: 10px; position: relative; left: 0; top: 0;
	color: #444; font-size: 12px; text-shadow: #ccc 1px 1px;
	border-radius: 6px; -webkit-border-radius: 6px; -moz-border-radius: 6px; }
.notification-body:after { content: ""; position: absolute; top: 100%; left: 50%; margin-left: -3px; border-top: 10px solid transparent;
    border-top-color: inherit; border-left: 6px solid transparent; border-right: 6px solid transparent; }
.title { border-bottom: 1px solid #f84; font-size: 16px; font-weight: bold; padding-bottom: 5px; margin-bottom: 8px; }
.notification .x { position: absolute; right: 4px; top: 0; cursor: pointer; font-size: 16px; color: #f84; text-shadow: none; }
.notification.classic .notification-body { width: 400px; background: #fea; border-color: #fea;}
.notification.short .title { display: none; }

.notification h1 { font-size: 14px; font-weight: bold; padding-top: 8px; padding-bottom: 8px; text-decoration: underline;}
.notification .connection { padding: 8px; margin: 8px; background: #fed; border: 1px solid #f84; }
.notification .connection.enabled { background: #dfd; }
.notification .connection:after { content: "OFF"; font-size: 10px; color: #a44; float: right; }
.notification .connection.enabled:after { content: "ON"; font-size: 10px; color: #4a4; float: right; }
.notification ul { list-style-type: upper-roman; }

.notification .pack {
    padding: 0px;
    margin: 2px;
    background: #fdd;
    border: 1px solid #f84;
    border-radius: 4px;
    cursor: pointer;
}
.notification .pack.enabled { background: #dfd; cursor: not-allowed; }
.notification .pack:after { content: ""; font-size: 10px; color: #a44; float: right; }
.notification .pack.enabled:after { content: "Selected"; font-size: 10px; color: #4a4; float: right; }


#modal { width: 100%; height: 100%; position: fixed; left: 0; top: 0; display: none; }
#modal .bg { width: 100%; height: 100%; background: #48a; opacity: 0.5; position: absolute; left: 0; top: 0; }
#modal, #modal * { user-select: text; -webkit-user-select: text; -moz-user-select: text; -ms-user-select: text; }

.dialog { background: #cdc; width: 550px; height: 250px; position: fixed; inset: 0;
    margin: auto; border: 1px solid #9a9; overflow: hidden;
	border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px;
	box-shadow: 0px 0px 8px #000; -webkit-box-shadow: 0px 0px 8px #000; -moz-box-shadow: 0px 0px 8px #000; }
.dialog p { margin: 10px; font-size: 20px; }
.dialog input.text { font-size: 20px; height: 20px; width: 75%; }
.dialog input.checkbox { margin: 0 5px; }
.dialog .submit { background: #fe4; border: none; padding: 7px 40px 20px 30px; font-size: 20px; color: #fff;
	text-shadow: #444 2px 2px 2px;
	border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px;
	box-shadow: inset 0px 0px 4px #000; -webkit-box-shadow: inset 0px 0px 4px #000; -moz-box-shadow: inset 0px 0px 4px #000;
	transition: all 0.25s; -webkit-transition: all 0.25s; -moz-transition: all 0.25s; -o-transition: all 0.25s;
	position: absolute; bottom: -10px; right: -10px; }
.dialog .submit:hover { background: #ff8; transition: all 0.25s; -webkit-transition: all 0.25s; -moz-transition: all 0.25s;
	-o-transition: all 0.25s; }

#chat { display: none; }
#chat { position: fixed; bottom: 64px; left: 0; width: 100%; vertical-align: bottom; font-size: 13px; color: white; text-shadow: #888 1px 1px; }
#chat, #chat * { user-select: text; -webkit-user-select: text; -moz-user-select: text; -ms-user-select: text; }
#chat ul { list-style: none; margin: 4px; padding: 0; }
#chat li { padding: 2px; opacity: 0; }
#chat li .name { font-weight: bold; margin-right: 10px; }
#chat li .message { margin-right: 6px; }
#chat li .quote { color: #789922; }

#chat li .mention {
    color: white;
    padding: 4px;
    margin: 2px;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    line-height: 15px;
    text-shadow: #444 1px 1px;
}

#chat input { margin: 4px; width: 99%; border: 1px solid #fff; background: none; text-shadow: #888 1px 1px; color: #fff; 
	border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px; }
#chat input::-webkit-input-placeholder { color: #ccc; }
#chat input:-moz-placeholder { color: #ccc; }
#chat input:focus { outline: none; border: 1px solid #ff8; }
#chat.chatting { background: rgba(64,80,80,0.75); border-radius: 5px; box-shadow: 1px 1px 5px #888; transition: all 0.1s; }
#chat.chatting li { display: list-item !important; opacity: 1 !important; text-shadow: #aaa 1px 1px; }
#chat.chatting ul { max-height: 50em; overflow-y: scroll; overflow-x: hidden; word-wrap: break-word; }

#crownsolo-notice { z-index: 1; }
#cursors { z-index: 2; }
#chat { z-index: 100; }
#social { z-index: 200; }
#names { z-index: 300; }
#piano { z-index: 400; }
#piano .key { z-index: 401; }
#piano .key.sharp { z-index: 402; }
#bottom { z-index: 500; }
#crown { z-index: 600; }
.notification { z-index: 700; }
#cursors .cursor { z-index: 800; }
#chat.chatting { z-index: 900; }
.participant-menu { z-index: 1000; }
#modal { z-index: 10000; }
#tooltip { z-index: 20000; }


.clear { clear: both; }

.spin { animation: spin 1s linear infinite; -webkit-animation: spin 1s linear infinite; -moz-animation: spin 1s linear infinite;
	-o-animation: spin 1s linear infinite; -ms-animation: spin 1s linear infinite; }

#debug {
	position: absolute;
	pointer-events: none;
	background-color: rgba(137, 137, 137, 0.414);
	color: #000000;
	/*backdrop-filter: blur(4px);*/
	font-size: 14px;
	z-index: 10001;
	top: 0px;
	/*width: 180px;*/
	right: 0px;
}

.dialog .userinfo p {
    color: #555;
}

@-webkit-keyframes spin {
0% { -webkit-transform: rotate(0deg);}
100% { -webkit-transform: rotate(360deg);}
}
@-moz-keyframes spin {
0% { -moz-transform: rotate(0deg);}
100% { -moz-transform: rotate(360deg);}
}
@-o-keyframes spin {
0% { -o-transform: rotate(0deg);}
100% { -o-transform: rotate(360deg);}
}
@-ms-keyframes spin {
0% { -ms-transform: rotate(0deg);}
100% { -ms-transform: rotate(360deg);}
}
 
