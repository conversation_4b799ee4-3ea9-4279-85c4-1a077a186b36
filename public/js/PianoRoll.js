function initPianoRoll() {
    const canvas = document.getElementById("pianoroll")
    const ctx = canvas.getContext("2d")
    const pixel = ctx.createImageData(480 * 2, 1)
    pixel.data.fill(0)
    let lastUpdate = 0

    const noteDB = {}
    const notes = [
        "c",
        "cs",
        "d",
        "ds",
        "e",
        "f",
        "fs",
        "g",
        "gs",
        "a",
        "as",
        "b",
    ]

    for (let octave = -2; octave <= 8; octave++) {
        for (let i = 0; i < notes.length; i++) {
            noteDB[`${notes[i]}${octave}`] = (octave + 2) * 12 + i
        }
    }

    window.pianoRollSpeed = 4

    window.redraw = () => {
        if (lastUpdate <= canvas.height) {
            ctx.globalCompositeOperation = "copy"
            ctx.drawImage(ctx.canvas, 0, -pianoRollSpeed)
            ctx.globalCompositeOperation = "source-over"

            for (let y = 0; y < pianoRollSpeed; y++) {
                ctx.putImageData(pixel, 0, canvas.height - y)
            }

            if (lastUpdate++ === 0) {
                pixel.data.fill(0)
            }
        }
        requestAnimationFrame(redraw)
    }

    redraw()

    window.showNote = (note, col, vel) => {
        if (noteDB.hasOwnProperty(note)) {
            lastUpdate = 0
            const idx = noteDB[note] * 8

            pixel.data[idx] = pixel.data[idx + 4] = col[0]
            pixel.data[idx + 1] = pixel.data[idx + 5] = col[1]
            pixel.data[idx + 2] = pixel.data[idx + 6] = col[2]
            pixel.data[idx + 3] = pixel.data[idx + 7] = Math.floor(
                vel * 205 + 50
            )
        }
    }

    $(function () {
        const colcache = {}
        const originalVisualize = MPP.piano.renderer.visualize

        MPP.piano.renderer.visualize = function (n, c, vel) {
            originalVisualize.call(this, n, c, vel)

            let co = colcache[c]
            if (!co) {
                co = [c[1] + c[2], c[3] + c[4], c[5] + c[6]].map((x) =>
                    parseInt(x, 16)
                )
                colcache[c] = co
            }
            showNote(n.note, co, vel)
        }
    })
}

$(initPianoRoll)
