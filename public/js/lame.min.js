function lamejs(){function T(e){return new Int32Array(e)}function K(e){return new Float32Array(e)}function qa(e){if(1==e.length)return K(e[0]);for(var p=e[0],e=e.slice(1),l=[],z=0;z<p;z++)l.push(qa(e));return l}function va(e){if(1==e.length)return T(e[0]);for(var p=e[0],e=e.slice(1),l=[],z=0;z<p;z++)l.push(va(e));return l}function nc(e){if(1==e.length)return new Int16Array(e[0]);for(var p=e[0],e=e.slice(1),l=[],z=0;z<p;z++)l.push(nc(e));return l}function ac(e){if(1==e.length)return Array(e[0]);for(var p=
e[0],e=e.slice(1),l=[],z=0;z<p;z++)l.push(ac(e));return l}function ya(e){this.ordinal=e}function F(e){this.ordinal=e}function ia(e){this.ordinal=function(){return e}}function Ac(){this.getLameVersion=function(){return"3.98.4"};this.getLameShortVersion=function(){return"3.98.4"};this.getLameVeryShortVersion=function(){return"LAME3.98r"};this.getPsyVersion=function(){return"0.93"};this.getLameUrl=function(){return"http://www.mp3dev.org/"};this.getLameOsBitness=function(){return"32bits"}}function Bc(){function e(b,
n,a,f,h,k,g,d,c,B,y,r,s,j,i){this.vbr_q=b;this.quant_comp=n;this.quant_comp_s=a;this.expY=f;this.st_lrm=h;this.st_s=k;this.masking_adj=g;this.masking_adj_short=d;this.ath_lower=c;this.ath_curve=B;this.ath_sensitivity=y;this.interch=r;this.safejoint=s;this.sfb21mod=j;this.msfix=i}function p(b,e,a,f,h,k,g,d,c,B,y,r,s,j){this.quant_comp=e;this.quant_comp_s=a;this.safejoint=f;this.nsmsfix=h;this.st_lrm=k;this.st_s=g;this.nsbass=d;this.scale=c;this.masking_adj=B;this.ath_lower=y;this.ath_curve=r;this.interch=
s;this.sfscale=j}function l(b,e,a){var f=b.VBR==F.vbr_rh?u:q,h=b.VBR_q_frac,k=f[e],f=f[e+1];k.st_lrm+=h*(f.st_lrm-k.st_lrm);k.st_s+=h*(f.st_s-k.st_s);k.masking_adj+=h*(f.masking_adj-k.masking_adj);k.masking_adj_short+=h*(f.masking_adj_short-k.masking_adj_short);k.ath_lower+=h*(f.ath_lower-k.ath_lower);k.ath_curve+=h*(f.ath_curve-k.ath_curve);k.ath_sensitivity+=h*(f.ath_sensitivity-k.ath_sensitivity);k.interch+=h*(f.interch-k.interch);k.msfix+=h*(f.msfix-k.msfix);f=k.vbr_q;0>f&&(f=0);9<f&&(f=9);b.VBR_q=
f;b.VBR_q_frac=0;0!=a?b.quant_comp=k.quant_comp:0<Math.abs(b.quant_comp- -1)||(b.quant_comp=k.quant_comp);0!=a?b.quant_comp_short=k.quant_comp_s:0<Math.abs(b.quant_comp_short- -1)||(b.quant_comp_short=k.quant_comp_s);0!=k.expY&&(b.experimentalY=0!=k.expY);0!=a?b.internal_flags.nsPsy.attackthre=k.st_lrm:0<Math.abs(b.internal_flags.nsPsy.attackthre- -1)||(b.internal_flags.nsPsy.attackthre=k.st_lrm);0!=a?b.internal_flags.nsPsy.attackthre_s=k.st_s:0<Math.abs(b.internal_flags.nsPsy.attackthre_s- -1)||
(b.internal_flags.nsPsy.attackthre_s=k.st_s);0!=a?b.maskingadjust=k.masking_adj:0<Math.abs(b.maskingadjust-0)||(b.maskingadjust=k.masking_adj);0!=a?b.maskingadjust_short=k.masking_adj_short:0<Math.abs(b.maskingadjust_short-0)||(b.maskingadjust_short=k.masking_adj_short);0!=a?b.ATHlower=-k.ath_lower/10:0<Math.abs(10*-b.ATHlower-0)||(b.ATHlower=-k.ath_lower/10);0!=a?b.ATHcurve=k.ath_curve:0<Math.abs(b.ATHcurve- -1)||(b.ATHcurve=k.ath_curve);0!=a?b.athaa_sensitivity=k.ath_sensitivity:0<Math.abs(b.athaa_sensitivity-
-1)||(b.athaa_sensitivity=k.ath_sensitivity);0<k.interch&&(0!=a?b.interChRatio=k.interch:0<Math.abs(b.interChRatio- -1)||(b.interChRatio=k.interch));0<k.safejoint&&(b.exp_nspsytune|=k.safejoint);0<k.sfb21mod&&(b.exp_nspsytune|=k.sfb21mod<<20);0!=a?b.msfix=k.msfix:0<Math.abs(b.msfix- -1)||(b.msfix=k.msfix);0==a&&(b.VBR_q=e,b.VBR_q_frac=h)}function z(b,e,a){var f=x.nearestBitrateFullIndex(e);b.VBR=F.vbr_abr;b.VBR_mean_bitrate_kbps=e;b.VBR_mean_bitrate_kbps=Math.min(b.VBR_mean_bitrate_kbps,320);b.VBR_mean_bitrate_kbps=
Math.max(b.VBR_mean_bitrate_kbps,8);b.brate=b.VBR_mean_bitrate_kbps;320<b.VBR_mean_bitrate_kbps&&(b.disable_reservoir=!0);0<h[f].safejoint&&(b.exp_nspsytune|=2);0<h[f].sfscale&&(b.internal_flags.noise_shaping=2);if(0<Math.abs(h[f].nsbass)){var q=int(4*h[f].nsbass);0>q&&(q+=64);b.exp_nspsytune|=q<<2}0!=a?b.quant_comp=h[f].quant_comp:0<Math.abs(b.quant_comp- -1)||(b.quant_comp=h[f].quant_comp);0!=a?b.quant_comp_short=h[f].quant_comp_s:0<Math.abs(b.quant_comp_short- -1)||(b.quant_comp_short=h[f].quant_comp_s);
0!=a?b.msfix=h[f].nsmsfix:0<Math.abs(b.msfix- -1)||(b.msfix=h[f].nsmsfix);0!=a?b.internal_flags.nsPsy.attackthre=h[f].st_lrm:0<Math.abs(b.internal_flags.nsPsy.attackthre- -1)||(b.internal_flags.nsPsy.attackthre=h[f].st_lrm);0!=a?b.internal_flags.nsPsy.attackthre_s=h[f].st_s:0<Math.abs(b.internal_flags.nsPsy.attackthre_s- -1)||(b.internal_flags.nsPsy.attackthre_s=h[f].st_s);0!=a?b.scale=h[f].scale:0<Math.abs(b.scale- -1)||(b.scale=h[f].scale);0!=a?b.maskingadjust=h[f].masking_adj:0<Math.abs(b.maskingadjust-
0)||(b.maskingadjust=h[f].masking_adj);0<h[f].masking_adj?0!=a?b.maskingadjust_short=0.9*h[f].masking_adj:0<Math.abs(b.maskingadjust_short-0)||(b.maskingadjust_short=0.9*h[f].masking_adj):0!=a?b.maskingadjust_short=1.1*h[f].masking_adj:0<Math.abs(b.maskingadjust_short-0)||(b.maskingadjust_short=1.1*h[f].masking_adj);0!=a?b.ATHlower=-h[f].ath_lower/10:0<Math.abs(10*-b.ATHlower-0)||(b.ATHlower=-h[f].ath_lower/10);0!=a?b.ATHcurve=h[f].ath_curve:0<Math.abs(b.ATHcurve- -1)||(b.ATHcurve=h[f].ath_curve);
0!=a?b.interChRatio=h[f].interch:0<Math.abs(b.interChRatio- -1)||(b.interChRatio=h[f].interch);return e}var x;this.setModules=function(b){x=b};var u=[new e(0,9,9,0,5.2,125,-4.2,-6.3,4.8,1,0,0,2,21,0.97),new e(1,9,9,0,5.3,125,-3.6,-5.6,4.5,1.5,0,0,2,21,1.35),new e(2,9,9,0,5.6,125,-2.2,-3.5,2.8,2,0,0,2,21,1.49),new e(3,9,9,1,5.8,130,-1.8,-2.8,2.6,3,-4,0,2,20,1.64),new e(4,9,9,1,6,135,-0.7,-1.1,1.1,3.5,-8,0,2,0,1.79),new e(5,9,9,1,6.4,140,0.5,0.4,-7.5,4,-12,2E-4,0,0,1.95),new e(6,9,9,1,6.6,145,0.67,
0.65,-14.7,6.5,-19,4E-4,0,0,2.3),new e(7,9,9,1,6.6,145,0.8,0.75,-19.7,8,-22,6E-4,0,0,2.7),new e(8,9,9,1,6.6,145,1.2,1.15,-27.5,10,-23,7E-4,0,0,0),new e(9,9,9,1,6.6,145,1.6,1.6,-36,11,-25,8E-4,0,0,0),new e(10,9,9,1,6.6,145,2,2,-36,12,-25,8E-4,0,0,0)],q=[new e(0,9,9,0,4.2,25,-7,-4,7.5,1,0,0,2,26,0.97),new e(1,9,9,0,4.2,25,-5.6,-3.6,4.5,1.5,0,0,2,21,1.35),new e(2,9,9,0,4.2,25,-4.4,-1.8,2,2,0,0,2,18,1.49),new e(3,9,9,1,4.2,25,-3.4,-1.25,1.1,3,-4,0,2,15,1.64),new e(4,9,9,1,4.2,25,-2.2,0.1,0,3.5,-8,0,2,
0,1.79),new e(5,9,9,1,4.2,25,-1,1.65,-7.7,4,-12,2E-4,0,0,1.95),new e(6,9,9,1,4.2,25,-0,2.47,-7.7,6.5,-19,4E-4,0,0,2),new e(7,9,9,1,4.2,25,0.5,2,-14.5,8,-22,6E-4,0,0,2),new e(8,9,9,1,4.2,25,1,2.4,-22,10,-23,7E-4,0,0,2),new e(9,9,9,1,4.2,25,1.5,2.95,-30,11,-25,8E-4,0,0,2),new e(10,9,9,1,4.2,25,2,2.95,-36,12,-30,8E-4,0,0,2)],h=[new p(8,9,9,0,0,6.6,145,0,0.95,0,-30,11,0.0012,1),new p(16,9,9,0,0,6.6,145,0,0.95,0,-25,11,0.001,1),new p(24,9,9,0,0,6.6,145,0,0.95,0,-20,11,0.001,1),new p(32,9,9,0,0,6.6,145,
0,0.95,0,-15,11,0.001,1),new p(40,9,9,0,0,6.6,145,0,0.95,0,-10,11,9E-4,1),new p(48,9,9,0,0,6.6,145,0,0.95,0,-10,11,9E-4,1),new p(56,9,9,0,0,6.6,145,0,0.95,0,-6,11,8E-4,1),new p(64,9,9,0,0,6.6,145,0,0.95,0,-2,11,8E-4,1),new p(80,9,9,0,0,6.6,145,0,0.95,0,0,8,7E-4,1),new p(96,9,9,0,2.5,6.6,145,0,0.95,0,1,5.5,6E-4,1),new p(112,9,9,0,2.25,6.6,145,0,0.95,0,2,4.5,5E-4,1),new p(128,9,9,0,1.95,6.4,140,0,0.95,0,3,4,2E-4,1),new p(160,9,9,1,1.79,6,135,0,0.95,-2,5,3.5,0,1),new p(192,9,9,1,1.49,5.6,125,0,0.97,
-4,7,3,0,0),new p(224,9,9,1,1.25,5.2,125,0,0.98,-6,9,2,0,0),new p(256,9,9,1,0.97,5.2,125,0,1,-8,10,1,0,0),new p(320,9,9,1,0.9,5.2,125,0,1,-10,12,0,0,0)];this.apply_preset=function(b,e,a){switch(e){case E.R3MIX:e=E.V3;b.VBR=F.vbr_mtrh;break;case E.MEDIUM:e=E.V4;b.VBR=F.vbr_rh;break;case E.MEDIUM_FAST:e=E.V4;b.VBR=F.vbr_mtrh;break;case E.STANDARD:e=E.V2;b.VBR=F.vbr_rh;break;case E.STANDARD_FAST:e=E.V2;b.VBR=F.vbr_mtrh;break;case E.EXTREME:e=E.V0;b.VBR=F.vbr_rh;break;case E.EXTREME_FAST:e=E.V0;b.VBR=
F.vbr_mtrh;break;case E.INSANE:return e=320,b.preset=e,z(b,e,a),b.VBR=F.vbr_off,e}b.preset=e;switch(e){case E.V9:return l(b,9,a),e;case E.V8:return l(b,8,a),e;case E.V7:return l(b,7,a),e;case E.V6:return l(b,6,a),e;case E.V5:return l(b,5,a),e;case E.V4:return l(b,4,a),e;case E.V3:return l(b,3,a),e;case E.V2:return l(b,2,a),e;case E.V1:return l(b,1,a),e;case E.V0:return l(b,0,a),e}if(8<=e&&320>=e)return z(b,e,a);b.preset=0;return e}}function U(){function e(q,h,b,n,a,f){for(;0!=a--;)b[n]=1E-10+q[h+
0]*f[0]-b[n-1]*f[1]+q[h-1]*f[2]-b[n-2]*f[3]+q[h-2]*f[4]-b[n-3]*f[5]+q[h-3]*f[6]-b[n-4]*f[7]+q[h-4]*f[8]-b[n-5]*f[9]+q[h-5]*f[10]-b[n-6]*f[11]+q[h-6]*f[12]-b[n-7]*f[13]+q[h-7]*f[14]-b[n-8]*f[15]+q[h-8]*f[16]-b[n-9]*f[17]+q[h-9]*f[18]-b[n-10]*f[19]+q[h-10]*f[20],++n,++h}function p(e,h,b,n,a,f){for(;0!=a--;)b[n]=e[h+0]*f[0]-b[n-1]*f[1]+e[h-1]*f[2]-b[n-2]*f[3]+e[h-2]*f[4],++n,++h}var l=U.RMS_WINDOW_TIME_NUMERATOR,z=U.RMS_WINDOW_TIME_DENOMINATOR,x=[[0.038575994352,-3.84664617118067,-0.02160367184185,7.81501653005538,
-0.00123395316851,-11.34170355132042,-9.291677959E-5,13.05504219327545,-0.01655260341619,-12.28759895145294,0.02161526843274,9.4829380631979,-0.02074045215285,-5.87257861775999,0.00594298065125,2.75465861874613,0.00306428023191,-0.86984376593551,1.2025322027E-4,0.13919314567432,0.00288463683916],[0.0541865640643,-3.47845948550071,-0.02911007808948,6.36317777566148,-0.00848709379851,-8.54751527471874,-0.00851165645469,9.4769360780128,-0.00834990904936,-8.81498681370155,0.02245293253339,6.85401540936998,
-0.02596338512915,-4.39470996079559,0.01624864962975,2.19611684890774,-0.00240879051584,-0.75104302451432,0.00674613682247,0.13149317958808,-0.00187763777362],[0.15457299681924,-2.37898834973084,-0.09331049056315,2.84868151156327,-0.06247880153653,-2.64577170229825,0.02163541888798,2.23697657451713,-0.05588393329856,-1.67148153367602,0.04781476674921,1.00595954808547,0.00222312597743,-0.45953458054983,0.03174092540049,0.16378164858596,-0.01390589421898,-0.05032077717131,0.00651420667831,0.0234789740702,
-0.00881362733839],[0.30296907319327,-1.61273165137247,-0.22613988682123,1.0797749225997,-0.08587323730772,-0.2565625775407,0.03282930172664,-0.1627671912044,-0.00915702933434,-0.22638893773906,-0.02364141202522,0.39120800788284,-0.00584456039913,-0.22138138954925,0.06276101321749,0.04500235387352,-8.28086748E-6,0.02005851806501,0.00205861885564,0.00302439095741,-0.02950134983287],[0.33642304856132,-1.49858979367799,-0.2557224142557,0.87350271418188,-0.11828570177555,0.12205022308084,0.11921148675203,
-0.80774944671438,-0.07834489609479,0.47854794562326,-0.0046997791438,-0.12453458140019,-0.0058950022444,-0.04067510197014,0.05724228140351,0.08333755284107,0.00832043980773,-0.04237348025746,-0.0163538138454,0.02977207319925,-0.0176017656815],[0.4491525660845,-0.62820619233671,-0.14351757464547,0.29661783706366,-0.22784394429749,-0.372563729424,-0.01419140100551,0.00213767857124,0.04078262797139,-0.42029820170918,-0.12398163381748,0.22199650564824,0.04097565135648,0.00613424350682,0.10478503600251,
0.06747620744683,-0.01863887810927,0.05784820375801,-0.03193428438915,0.03222754072173,0.00541907748707],[0.56619470757641,-1.04800335126349,-0.75464456939302,0.29156311971249,0.1624213774223,-0.26806001042947,0.16744243493672,0.00819999645858,-0.18901604199609,0.45054734505008,0.3093178284183,-0.33032403314006,-0.27562961986224,0.0673936833311,0.00647310677246,-0.04784254229033,0.08647503780351,0.01639907836189,-0.0378898455484,0.01807364323573,-0.00588215443421],[0.58100494960553,-0.51035327095184,
-0.53174909058578,-0.31863563325245,-0.14289799034253,-0.20256413484477,0.17520704835522,0.1472815413433,0.02377945217615,0.38952639978999,0.15558449135573,-0.23313271880868,-0.25344790059353,-0.05246019024463,0.01628462406333,-0.02505961724053,0.06920467763959,0.02442357316099,-0.03721611395801,0.01818801111503,-0.00749618797172],[0.53648789255105,-0.2504987195602,-0.42163034350696,-0.43193942311114,-0.00275953611929,-0.03424681017675,0.04267842219415,-0.04678328784242,-0.10214864179676,0.26408300200955,
0.14590772289388,0.15113130533216,-0.02459864859345,-0.17556493366449,-0.11202315195388,-0.18823009262115,-0.04060034127,0.05477720428674,0.0478866554818,0.0470440968812,-0.02217936801134]],u=[[0.98621192462708,-1.97223372919527,-1.97242384925416,0.97261396931306,0.98621192462708],[0.98500175787242,-1.96977855582618,-1.97000351574484,0.9702284756635,0.98500175787242],[0.97938932735214,-1.95835380975398,-1.95877865470428,0.95920349965459,0.97938932735214],[0.97531843204928,-1.95002759149878,-1.95063686409857,
0.95124613669835,0.97531843204928],[0.97316523498161,-1.94561023566527,-1.94633046996323,0.94705070426118,0.97316523498161],[0.96454515552826,-1.92783286977036,-1.92909031105652,0.93034775234268,0.96454515552826],[0.96009142950541,-1.91858953033784,-1.92018285901082,0.92177618768381,0.96009142950541],[0.95856916599601,-1.9154210807478,-1.91713833199203,0.91885558323625,0.95856916599601],[0.94597685600279,-1.88903307939452,-1.89195371200558,0.89487434461664,0.94597685600279]];this.InitGainAnalysis=
function(e,h){var b;a:{for(b=0;b<MAX_ORDER;b++)e.linprebuf[b]=e.lstepbuf[b]=e.loutbuf[b]=e.rinprebuf[b]=e.rstepbuf[b]=e.routbuf[b]=0;switch(0|h){case 48E3:e.reqindex=0;break;case 44100:e.reqindex=1;break;case 32E3:e.reqindex=2;break;case 24E3:e.reqindex=3;break;case 22050:e.reqindex=4;break;case 16E3:e.reqindex=5;break;case 12E3:e.reqindex=6;break;case 11025:e.reqindex=7;break;case 8E3:e.reqindex=8;break;default:b=INIT_GAIN_ANALYSIS_ERROR;break a}e.sampleWindow=0|(h*l+z-1)/z;e.lsum=0;e.rsum=0;e.totsamp=
0;Ba.ill(e.A,0);b=INIT_GAIN_ANALYSIS_OK}if(b!=INIT_GAIN_ANALYSIS_OK)return INIT_GAIN_ANALYSIS_ERROR;e.linpre=MAX_ORDER;e.rinpre=MAX_ORDER;e.lstep=MAX_ORDER;e.rstep=MAX_ORDER;e.lout=MAX_ORDER;e.rout=MAX_ORDER;Ba.fill(e.B,0);return INIT_GAIN_ANALYSIS_OK};this.AnalyzeSamples=function(q,h,b,n,a,f,l){var k,g,d,c,B,y;if(0==f)return GAIN_ANALYSIS_OK;y=0;c=f;switch(l){case 1:n=h;a=b;break;case 2:break;default:return GAIN_ANALYSIS_ERROR}f<MAX_ORDER?(N.arraycopy(h,b,q.linprebuf,MAX_ORDER,f),N.arraycopy(n,a,
q.rinprebuf,MAX_ORDER,f)):(N.arraycopy(h,b,q.linprebuf,MAX_ORDER,MAX_ORDER),N.arraycopy(n,a,q.rinprebuf,MAX_ORDER,MAX_ORDER));for(;0<c;){B=c>q.sampleWindow-q.totsamp?q.sampleWindow-q.totsamp:c;y<MAX_ORDER?(l=q.linpre+y,k=q.linprebuf,g=q.rinpre+y,d=q.rinprebuf,B>MAX_ORDER-y&&(B=MAX_ORDER-y)):(l=b+y,k=h,g=a+y,d=n);e(k,l,q.lstepbuf,q.lstep+q.totsamp,B,x[q.reqindex]);e(d,g,q.rstepbuf,q.rstep+q.totsamp,B,x[q.reqindex]);p(q.lstepbuf,q.lstep+q.totsamp,q.loutbuf,q.lout+q.totsamp,B,u[q.reqindex]);p(q.rstepbuf,
q.rstep+q.totsamp,q.routbuf,q.rout+q.totsamp,B,u[q.reqindex]);l=q.lout+q.totsamp;k=q.loutbuf;g=q.rout+q.totsamp;d=q.routbuf;for(var r=B%8;0!=r--;){var s=q,j=s.lsum,i=k[l++];s.lsum=j+i*i;s=q;j=s.rsum;i=d[g++];s.rsum=j+i*i}for(r=B/8;0!=r--;)q.lsum+=k[l+0]*k[l+0]+k[l+1]*k[l+1]+k[l+2]*k[l+2]+k[l+3]*k[l+3]+k[l+4]*k[l+4]+k[l+5]*k[l+5]+k[l+6]*k[l+6]+k[l+7]*k[l+7],l+=8,q.rsum+=d[g+0]*d[g+0]+d[g+1]*d[g+1]+d[g+2]*d[g+2]+d[g+3]*d[g+3]+d[g+4]*d[g+4]+d[g+5]*d[g+5]+d[g+6]*d[g+6]+d[g+7]*d[g+7],g+=8;c-=B;y+=B;q.totsamp+=
B;q.totsamp==q.sampleWindow&&(l=10*U.STEPS_per_dB*Math.log10(0.5*((q.lsum+q.rsum)/q.totsamp)+1E-37),l=0>=l?0:0|l,l>=q.A.length&&(l=q.A.length-1),q.A[l]++,q.lsum=q.rsum=0,N.arraycopy(q.loutbuf,q.totsamp,q.loutbuf,0,MAX_ORDER),N.arraycopy(q.routbuf,q.totsamp,q.routbuf,0,MAX_ORDER),N.arraycopy(q.lstepbuf,q.totsamp,q.lstepbuf,0,MAX_ORDER),N.arraycopy(q.rstepbuf,q.totsamp,q.rstepbuf,0,MAX_ORDER),q.totsamp=0);if(q.totsamp>q.sampleWindow)return GAIN_ANALYSIS_ERROR}f<MAX_ORDER?(N.arraycopy(q.linprebuf,f,
q.linprebuf,0,MAX_ORDER-f),N.arraycopy(q.rinprebuf,f,q.rinprebuf,0,MAX_ORDER-f),N.arraycopy(h,b,q.linprebuf,MAX_ORDER-f,f),N.arraycopy(n,a,q.rinprebuf,MAX_ORDER-f,f)):(N.arraycopy(h,b+f-MAX_ORDER,q.linprebuf,0,MAX_ORDER),N.arraycopy(n,a+f-MAX_ORDER,q.rinprebuf,0,MAX_ORDER));return GAIN_ANALYSIS_OK};this.GetTitleGain=function(e){var h;h=e.A;var b=e.A.length,n,a=0;for(n=0;n<b;n++)a+=h[n];if(0==a)h=GAIN_NOT_ENOUGH_SAMPLES;else{a=0|Math.ceil(a*(1-0.95));for(n=b;0<n--&&!(0>=(a-=h[n])););h=64.82-n/U.STEPS_per_dB}for(b=
0;b<e.A.length;b++)e.B[b]+=e.A[b],e.A[b]=0;for(b=0;b<MAX_ORDER;b++)e.linprebuf[b]=e.lstepbuf[b]=e.loutbuf[b]=e.rinprebuf[b]=e.rstepbuf[b]=e.routbuf[b]=0;e.totsamp=0;e.lsum=e.rsum=0;return h}}function Cc(){var e;this.setModules=function(p){e=p};this.ResvFrameBegin=function(p,l){var z=p.internal_flags,x,u=z.l3_side,q=e.getframebits(p);l.bits=(q-8*z.sideinfo_len)/z.mode_gr;var h=2048*z.mode_gr-8;320<p.brate?x=8*int(1E3*p.brate/(p.out_samplerate/1152)/8+0.5):(x=11520,p.strict_ISO&&(x=8*int(32E4/(p.out_samplerate/
1152)/8+0.5)));z.ResvMax=x-q;z.ResvMax>h&&(z.ResvMax=h);if(0>z.ResvMax||p.disable_reservoir)z.ResvMax=0;q=l.bits*z.mode_gr+Math.min(z.ResvSize,z.ResvMax);q>x&&(q=x);u.resvDrain_pre=0;null!=z.pinfo&&(z.pinfo.mean_bits=l.bits/2,z.pinfo.resvsize=z.ResvSize);return q};this.ResvMaxBits=function(e,l,D,x){var u=e.internal_flags,q=u.ResvSize,h=u.ResvMax;0!=x&&(q+=l);0!=(u.substep_shaping&1)&&(h*=0.9);D.bits=l;10*q>9*h?(x=q-9*h/10,D.bits+=x,u.substep_shaping|=128):(x=0,u.substep_shaping&=127,!e.disable_reservoir&&
0==(u.substep_shaping&1)&&(D.bits-=0.1*l));e=q<6*u.ResvMax/10?q:6*u.ResvMax/10;e-=x;0>e&&(e=0);return e};this.ResvAdjust=function(e,l){e.ResvSize-=l.part2_3_length+l.part2_length};this.ResvFrameEnd=function(e,l){var D,x=e.l3_side;e.ResvSize+=l*e.mode_gr;var u=0;x.resvDrain_post=0;x.resvDrain_pre=0;if(0!=(D=e.ResvSize%8))u+=D;D=e.ResvSize-u-e.ResvMax;0<D&&(u+=D);D=Math.min(8*x.main_data_begin,u)/8;x.resvDrain_pre+=8*D;u-=8*D;e.ResvSize-=8*D;x.main_data_begin-=D;x.resvDrain_post+=u;e.ResvSize-=u}}function La(){function D(a){this.bits=
0|a}function p(a,c,b,e,j,i){c=0.5946/c;for(a>>=1;0!=a--;)j[i++]=c>b[e++]?0:1,j[i++]=c>b[e++]?0:1}function l(a,c,b,e,j,i){for(var a=a>>1,m=a%2,a=a>>1;0!=a--;){var d,t,g,f,h,n,k;d=b[e++]*c;t=b[e++]*c;h=0|d;g=b[e++]*c;n=0|t;f=b[e++]*c;k=0|g;d+=u.adj43[h];h=0|f;t+=u.adj43[n];j[i++]=0|d;g+=u.adj43[k];j[i++]=0|t;f+=u.adj43[h];j[i++]=0|g;j[i++]=0|f}0!=m&&(d=b[e++]*c,t=b[e++]*c,d+=u.adj43[0|d],t+=u.adj43[0|t],j[i++]=0|d,j[i++]=0|t)}function z(a,c,b,e){var d,i=c,m=d=0;do{var w=a[i++],t=a[i++];d<w&&(d=w);m<
t&&(m=t)}while(i<b);d<m&&(d=m);switch(d){case 0:return d;case 1:i=c;c=0;d=v.ht[1].hlen;do m=2*a[i+0]+a[i+1],i+=2,c+=d[m];while(i<b);e.bits+=c;return 1;case 2:case 3:i=c;c=h[d-1];d=0;m=v.ht[c].xlen;w=2==c?v.table23:v.table56;do t=a[i+0]*m+a[i+1],i+=2,d+=w[t];while(i<b);a=d&65535;d>>=16;d>a&&(d=a,c++);e.bits+=d;return c;case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:var i=c,c=h[d-1],w=m=d=0,t=v.ht[c].xlen,g=v.ht[c].hlen,f=v.ht[c+1].hlen,n=v.ht[c+2].hlen;do{var k=
a[i+0]*t+a[i+1],i=i+2;d+=g[k];m+=f[k];w+=n[k]}while(i<b);a=c;d>m&&(d=m,a++);d>w&&(d=w,a=c+2);e.bits+=d;return a;default:if(d>ca.IXMAX_VAL)return e.bits=ca.LARGE_BITS,-1;d-=15;for(i=24;32>i&&!(v.ht[i].linmax>=d);i++);for(m=i-8;24>m&&!(v.ht[m].linmax>=d);m++);d=m;w=65536*v.ht[d].xlen+v.ht[i].xlen;m=0;do t=a[c++],g=a[c++],0!=t&&(14<t&&(t=15,m+=w),t*=16),0!=g&&(14<g&&(g=15,m+=w),t+=g),m+=v.largetbl[t];while(c<b);a=m&65535;m>>=16;m>a&&(m=a,d=i);e.bits+=m;return d}}function x(a,c,b,d,j,i,m,w){for(var t=
c.big_values,g=2;g<e.SBMAX_l+1;g++){var f=a.scalefac_band.l[g];if(f>=t)break;var h=j[g-2]+c.count1bits;if(b.part2_3_length<=h)break;h=new D(h);f=z(d,f,t,h);h=h.bits;b.part2_3_length<=h||(b.assign(c),b.part2_3_length=h,b.region0_count=i[g-2],b.region1_count=g-2-i[g-2],b.table_select[0]=m[g-2],b.table_select[1]=w[g-2],b.table_select[2]=f)}}var u=null;this.qupvt=null;this.setModules=function(a){u=this.qupvt=a};var q=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],
[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]],h=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];this.noquant_count_bits=function(a,c,b){var d=c.l3_enc,j=Math.min(576,c.max_nonzero_coeff+2>>1<<1);null!=b&&(b.sfb_count1=0);for(;1<j&&0==(d[j-1]|d[j-2]);j-=2);c.count1=j;for(var i=0,m=0;3<j;j-=4){var w;if(1<((d[j-1]|d[j-2]|d[j-3]|d[j-4])&2147483647))break;w=2*(2*(2*d[j-4]+d[j-3])+d[j-2])+d[j-1];i+=v.t32l[w];m+=v.t33l[w]}w=i;c.count1table_select=0;i>m&&(w=m,c.count1table_select=1);c.count1bits=w;c.big_values=
j;if(0==j)return w;c.block_type==e.SHORT_TYPE?(i=3*a.scalefac_band.s[3],i>c.big_values&&(i=c.big_values),m=c.big_values):c.block_type==e.NORM_TYPE?(i=c.region0_count=a.bv_scf[j-2],m=c.region1_count=a.bv_scf[j-1],m=a.scalefac_band.l[i+m+2],i=a.scalefac_band.l[i+1],m<j&&(w=new D(w),c.table_select[2]=z(d,m,j,w),w=w.bits)):(c.region0_count=7,c.region1_count=e.SBMAX_l-1-7-1,i=a.scalefac_band.l[8],m=j,i>m&&(i=m));i=Math.min(i,j);m=Math.min(m,j);0<i&&(w=new D(w),c.table_select[0]=z(d,0,i,w),w=w.bits);i<
m&&(w=new D(w),c.table_select[1]=z(d,i,m,w),w=w.bits);2==a.use_best_huffman&&(c.part2_3_length=w,best_huffman_divide(a,c),w=c.part2_3_length);if(null!=b&&c.block_type==e.NORM_TYPE){for(d=0;a.scalefac_band.l[d]<c.big_values;)d++;b.sfb_count1=d}return w};this.count_bits=function(a,c,b,d){var j=b.l3_enc,i=ca.IXMAX_VAL/u.IPOW20(b.global_gain);if(b.xrpow_max>i)return ca.LARGE_BITS;var i=u.IPOW20(b.global_gain),m,w,t=0,g,f=0,h=0,n=0,k=0,q=j,D=0,J=c,O=0;g=null!=d&&b.global_gain==d.global_gain;w=b.block_type==
e.SHORT_TYPE?38:21;for(m=0;m<=w;m++){var V=-1;if(g||b.block_type==e.NORM_TYPE)V=b.global_gain-(b.scalefac[m]+(0!=b.preflag?u.pretab[m]:0)<<b.scalefac_scale+1)-8*b.subblock_gain[b.window[m]];if(g&&d.step[m]==V)0!=f&&(l(f,i,J,O,q,D),f=0),0!=h&&(p(h,i,J,O,q,D),h=0);else{var da=b.width[m];t+b.width[m]>b.max_nonzero_coeff&&(m=b.max_nonzero_coeff-t+1,Ba.fill(j,b.max_nonzero_coeff,576,0),da=m,0>da&&(da=0),m=w+1);0==f&&0==h&&(q=j,D=k,J=c,O=n);null!=d&&0<d.sfb_count1&&m>=d.sfb_count1&&0<d.step[m]&&V>=d.step[m]?
(0!=f&&(l(f,i,J,O,q,D),f=0,q=j,D=k,J=c,O=n),h+=da):(0!=h&&(p(h,i,J,O,q,D),h=0,q=j,D=k,J=c,O=n),f+=da);if(0>=da){0!=h&&(p(h,i,J,O,q,D),h=0);0!=f&&(l(f,i,J,O,q,D),f=0);break}}m<=w&&(k+=b.width[m],n+=b.width[m],t+=b.width[m])}0!=f&&l(f,i,J,O,q,D);0!=h&&p(h,i,J,O,q,D);if(0!=(a.substep_shaping&2)){i=0;w=0.634521682242439/u.IPOW20(b.global_gain+b.scalefac_scale);for(t=0;t<b.sfbmax;t++)if(g=b.width[t],0==a.pseudohalf[t])i+=g;else{f=i;for(i+=g;f<i;++f)j[f]=c[f]>=w?j[f]:0}}return this.noquant_count_bits(a,
b,d)};this.best_huffman_divide=function(a,c){var b=new gb,d=c.l3_enc,j=T(23),i=T(23),m=T(23),w=T(23);if(!(c.block_type==e.SHORT_TYPE&&1==a.mode_gr)){b.assign(c);if(c.block_type==e.NORM_TYPE){for(var t=c.big_values,g=0;22>=g;g++)j[g]=ca.LARGE_BITS;for(g=0;16>g;g++){var f=a.scalefac_band.l[g+1];if(f>=t)break;for(var h=0,n=new D(h),k=z(d,0,f,n),h=n.bits,l=0;8>l;l++){var p=a.scalefac_band.l[g+l+2];if(p>=t)break;n=h;n=new D(n);p=z(d,f,p,n);n=n.bits;j[g+l]>n&&(j[g+l]=n,i[g+l]=g,m[g+l]=k,w[g+l]=p)}}x(a,
b,c,d,j,i,m,w)}t=b.big_values;if(!(0==t||1<(d[t-2]|d[t-1])))if(t=c.count1+2,!(576<t)){b.assign(c);b.count1=t;for(f=g=0;t>b.big_values;t-=4)h=2*(2*(2*d[t-4]+d[t-3])+d[t-2])+d[t-1],g+=v.t32l[h],f+=v.t33l[h];b.big_values=t;b.count1table_select=0;g>f&&(g=f,b.count1table_select=1);b.count1bits=g;b.block_type==e.NORM_TYPE?x(a,b,c,d,j,i,m,w):(b.part2_3_length=g,g=a.scalefac_band.l[8],g>t&&(g=t),0<g&&(j=new D(b.part2_3_length),b.table_select[0]=z(d,0,g,j),b.part2_3_length=j.bits),t>g&&(j=new D(b.part2_3_length),
b.table_select[1]=z(d,g,t,j),b.part2_3_length=j.bits),c.part2_3_length>b.part2_3_length&&c.assign(b))}}};var b=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],n=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],a=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],f=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];La.slen1_tab=a;La.slen2_tab=f;this.best_scalefac_store=function(c,d,g,s){var j=s.tt[d][g],i,m,w,t=0;for(i=m=0;i<j.sfbmax;i++){w=j.width[i];m+=w;for(w=-w;0>w&&0==j.l3_enc[w+m];w++);0==w&&(j.scalefac[i]=t=-2)}if(0==j.scalefac_scale&&0==j.preflag){for(i=
m=0;i<j.sfbmax;i++)0<j.scalefac[i]&&(m|=j.scalefac[i]);if(0==(m&1)&&0!=m){for(i=0;i<j.sfbmax;i++)0<j.scalefac[i]&&(j.scalefac[i]>>=1);j.scalefac_scale=t=1}}if(0==j.preflag&&j.block_type!=e.SHORT_TYPE&&2==c.mode_gr){for(i=11;i<e.SBPSY_l&&!(j.scalefac[i]<u.pretab[i]&&-2!=j.scalefac[i]);i++);if(i==e.SBPSY_l){for(i=11;i<e.SBPSY_l;i++)0<j.scalefac[i]&&(j.scalefac[i]-=u.pretab[i]);j.preflag=t=1}}for(i=0;4>i;i++)s.scfsi[g][i]=0;if(2==c.mode_gr&&1==d&&s.tt[0][g].block_type!=e.SHORT_TYPE&&s.tt[1][g].block_type!=
e.SHORT_TYPE){d=s.tt[1][g];m=s.tt[0][g];for(t=0;t<v.scfsi_band.length-1;t++){for(i=v.scfsi_band[t];i<v.scfsi_band[t+1]&&!(m.scalefac[i]!=d.scalefac[i]&&0<=d.scalefac[i]);i++);if(i==v.scfsi_band[t+1]){for(i=v.scfsi_band[t];i<v.scfsi_band[t+1];i++)d.scalefac[i]=-1;s.scfsi[g][t]=1}}for(i=s=g=0;11>i;i++)-1!=d.scalefac[i]&&(s++,g<d.scalefac[i]&&(g=d.scalefac[i]));for(w=m=0;i<e.SBPSY_l;i++)-1!=d.scalefac[i]&&(w++,m<d.scalefac[i]&&(m=d.scalefac[i]));for(t=0;16>t;t++)g<b[t]&&m<n[t]&&(i=a[t]*s+f[t]*w,d.part2_length>
i&&(d.part2_length=i,d.scalefac_compress=t));t=0}for(i=0;i<j.sfbmax;i++)-2==j.scalefac[i]&&(j.scalefac[i]=0);0!=t&&(2==c.mode_gr?this.scale_bitcount(j):this.scale_bitcount_lsf(c,j))};var J=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],k=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],g=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(a){var c,d=0,s=0,j,i=a.scalefac;if(a.block_type==e.SHORT_TYPE)j=J,0!=a.mixed_block_flag&&(j=k);else if(j=g,0==a.preflag){for(c=
11;c<e.SBPSY_l&&!(i[c]<u.pretab[c]);c++);if(c==e.SBPSY_l){a.preflag=1;for(c=11;c<e.SBPSY_l;c++)i[c]-=u.pretab[c]}}for(c=0;c<a.sfbdivide;c++)d<i[c]&&(d=i[c]);for(;c<a.sfbmax;c++)s<i[c]&&(s=i[c]);a.part2_length=ca.LARGE_BITS;for(c=0;16>c;c++)d<b[c]&&(s<n[c]&&a.part2_length>j[c])&&(a.part2_length=j[c],a.scalefac_compress=c);return a.part2_length==ca.LARGE_BITS};var d=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(a,b){var g,s,j,i,m,w,t,f=T(4),h=
b.scalefac;g=0!=b.preflag?2:0;for(w=0;4>w;w++)f[w]=0;if(b.block_type==e.SHORT_TYPE){s=1;var n=u.nr_of_sfb_block[g][s];for(j=t=0;4>j;j++){i=n[j]/3;for(w=0;w<i;w++,t++)for(m=0;3>m;m++)h[3*t+m]>f[j]&&(f[j]=h[3*t+m])}}else{s=0;n=u.nr_of_sfb_block[g][s];for(j=t=0;4>j;j++){i=n[j];for(w=0;w<i;w++,t++)h[t]>f[j]&&(f[j]=h[t])}}i=!1;for(j=0;4>j;j++)f[j]>d[g][j]&&(i=!0);if(!i){b.sfb_partition_table=u.nr_of_sfb_block[g][s];for(j=0;4>j;j++)b.slen[j]=c[f[j]];s=b.slen[0];j=b.slen[1];f=b.slen[2];m=b.slen[3];switch(g){case 0:b.scalefac_compress=
(5*s+j<<4)+(f<<2)+m;break;case 1:b.scalefac_compress=400+(5*s+j<<2)+f;break;case 2:b.scalefac_compress=500+3*s+j;break;default:N.err.printf("intensity stereo not implemented yet\n")}}if(!i)for(j=b.part2_length=0;4>j;j++)b.part2_length+=b.slen[j]*b.sfb_partition_table[j];return i};var c=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(a){for(var c=2;576>=c;c+=2){for(var b=0,d;a.scalefac_band.l[++b]<c;);for(d=q[b][0];a.scalefac_band.l[d+1]>c;)d--;0>d&&(d=q[b][0]);a.bv_scf[c-2]=d;for(d=q[b][1];a.scalefac_band.l[d+
a.bv_scf[c-2]+2]>c;)d--;0>d&&(d=q[b][1]);a.bv_scf[c-1]=d}}}function ka(){function D(a,b,d){for(;0<d;){var m;0==r&&(r=8,y++,a.header[a.w_ptr].write_timing==B&&(m=a,N.arraycopy(m.header[m.w_ptr].buf,0,c,y,m.sideinfo_len),y+=m.sideinfo_len,B+=8*m.sideinfo_len,m.w_ptr=m.w_ptr+1&aa.MAX_HEADER_BUF-1),c[y]=0);m=Math.min(d,r);d-=m;r-=m;c[y]|=b>>d<<r;B+=m}}function p(a,c){var b=a.internal_flags,m;8<=c&&(D(b,76,8),c-=8);8<=c&&(D(b,65,8),c-=8);8<=c&&(D(b,77,8),c-=8);8<=c&&(D(b,69,8),c-=8);if(32<=c){var d=g.getLameShortVersion();
if(32<=c)for(m=0;m<d.length&&8<=c;++m)c-=8,D(b,d.charAt(m),8)}for(;1<=c;c-=1)D(b,b.ancillary_flag,1),b.ancillary_flag^=!a.disable_reservoir?1:0}function l(a,c,b){for(var m=a.header[a.h_ptr].ptr;0<b;){var d=Math.min(b,8-(m&7)),b=b-d;a.header[a.h_ptr].buf[m>>3]|=c>>b<<8-(m&7)-d;m+=d}a.header[a.h_ptr].ptr=m}function z(a,c){for(var a=a<<8,b=0;8>b;b++)a<<=1,c<<=1,0!=((c^a)&65536)&&(c^=f);return c}function x(a,c){var b=v.ht[c.count1table_select+32],m,d=0,e=c.big_values,g=c.big_values;for(m=(c.count1-c.big_values)/
4;0<m;--m){var f=0,B=0,y;y=c.l3_enc[e+0];0!=y&&(B+=8,0>c.xr[g+0]&&f++);y=c.l3_enc[e+1];0!=y&&(B+=4,f*=2,0>c.xr[g+1]&&f++);y=c.l3_enc[e+2];0!=y&&(B+=2,f*=2,0>c.xr[g+2]&&f++);y=c.l3_enc[e+3];0!=y&&(B++,f*=2,0>c.xr[g+3]&&f++);e+=4;g+=4;D(a,f+b.table[B],b.hlen[B]);d+=b.hlen[B]}return d}function u(a,c,b,m,d){var e=v.ht[c],g=0;if(0==c)return g;for(;b<m;b+=2){var f=0,B=0,y=e.xlen,h=e.xlen,n=0,k=d.l3_enc[b],r=d.l3_enc[b+1];0!=k&&(0>d.xr[b]&&n++,f--);15<c&&(14<k&&(n|=k-15<<1,B=y,k=15),14<r&&(h=r-15,n<<=y,
n|=h,B+=y,r=15),h=16);0!=r&&(n<<=1,0>d.xr[b+1]&&n++,f--);k=k*h+r;B-=f;f+=e.hlen[k];D(a,e.table[k],f);D(a,n,B);g+=f+B}return g}function q(a,c){var b=3*a.scalefac_band.s[3];b>c.big_values&&(b=c.big_values);var m=u(a,c.table_select[0],0,b,c);return m+=u(a,c.table_select[1],b,c.big_values,c)}function h(a,c){var b,m,d,e;b=c.big_values;m=c.region0_count+1;d=a.scalefac_band.l[m];m+=c.region1_count+1;e=a.scalefac_band.l[m];d>b&&(d=b);e>b&&(e=b);m=u(a,c.table_select[0],0,d,c);m+=u(a,c.table_select[1],d,e,
c);return m+=u(a,c.table_select[2],e,b,c)}function b(){this.total=0}function n(c,b){var d=c.internal_flags,m,e,g,f;f=d.w_ptr;g=d.h_ptr-1;-1==g&&(g=aa.MAX_HEADER_BUF-1);m=d.header[g].write_timing-B;b.total=m;0<=m&&(e=1+g-f,g<f&&(e=1+g-f+aa.MAX_HEADER_BUF),m-=8*e*d.sideinfo_len);d=a.getframebits(c);m+=d;b.total+=d;b.total=0!=b.total%8?1+b.total/8:b.total/8;b.total+=y+1;0>m&&N.err.println("strange error flushing buffer ... \n");return m}var a=this,f=32773,J=null,k=null,g=null,d=null;this.setModules=
function(a,c,b,m){J=a;k=c;g=b;d=m};var c=null,B=0,y=0,r=0;this.getframebits=function(a){var c=a.internal_flags;return 8*(0|72E3*(a.version+1)*(0!=c.bitrate_index?v.bitrate_table[a.version][c.bitrate_index]:a.brate)/a.out_samplerate+c.padding)};this.CRC_writeheader=function(a,c){var b;b=z(c[2]&255,65535);b=z(c[3]&255,b);for(var m=6;m<a.sideinfo_len;m++)b=z(c[m]&255,b);c[4]=byte(b>>8);c[5]=byte(b&255)};this.flush_bitstream=function(a){var c=a.internal_flags,d,m;d=c.l3_side;if(!(0>(m=n(a,new b))))p(a,
m),c.ResvSize=0,d.main_data_begin=0,c.findReplayGain&&(d=J.GetTitleGain(c.rgdata),c.RadioGain=Math.floor(10*d+0.5)|0),c.findPeakSample&&(c.noclipGainChange=Math.ceil(200*Math.log10(c.PeakSample/32767))|0,c.noclipScale=0<c.noclipGainChange?EQ(a.scale,1)||EQ(a.scale,0)?Math.floor(100*(32767/c.PeakSample))/100:-1:-1)};this.add_dummy_byte=function(a,b,d){for(var a=a.internal_flags,m;0<d--;){m=b;for(var e=8;0<e;){var g;0==r&&(r=8,y++,c[y]=0);g=Math.min(e,r);e-=g;r-=g;c[y]|=m>>e<<r;B+=g}for(m=0;m<aa.MAX_HEADER_BUF;++m)a.header[m].write_timing+=
8}};this.format_bitstream=function(a){var c=a.internal_flags,d;d=c.l3_side;var m=this.getframebits(a);p(a,d.resvDrain_pre);var g=a.internal_flags,f,y,k;f=g.l3_side;g.header[g.h_ptr].ptr=0;Ba.fill(g.header[g.h_ptr].buf,0,g.sideinfo_len,0);16E3>a.out_samplerate?l(g,4094,12):l(g,4095,12);l(g,a.version,1);l(g,1,2);l(g,!a.error_protection?1:0,1);l(g,g.bitrate_index,4);l(g,g.samplerate_index,2);l(g,g.padding,1);l(g,a.extension,1);l(g,a.mode.ordinal(),2);l(g,g.mode_ext,2);l(g,a.copyright,1);l(g,a.original,
1);l(g,a.emphasis,2);a.error_protection&&l(g,0,16);if(1==a.version){l(g,f.main_data_begin,9);2==g.channels_out?l(g,f.private_bits,3):l(g,f.private_bits,5);for(k=0;k<g.channels_out;k++)for(y=0;4>y;y++)l(g,f.scfsi[k][y],1);for(y=0;2>y;y++)for(k=0;k<g.channels_out;k++){var r=f.tt[y][k];l(g,r.part2_3_length+r.part2_length,12);l(g,r.big_values/2,9);l(g,r.global_gain,8);l(g,r.scalefac_compress,4);r.block_type!=e.NORM_TYPE?(l(g,1,1),l(g,r.block_type,2),l(g,r.mixed_block_flag,1),14==r.table_select[0]&&(r.table_select[0]=
16),l(g,r.table_select[0],5),14==r.table_select[1]&&(r.table_select[1]=16),l(g,r.table_select[1],5),l(g,r.subblock_gain[0],3),l(g,r.subblock_gain[1],3),l(g,r.subblock_gain[2],3)):(l(g,0,1),14==r.table_select[0]&&(r.table_select[0]=16),l(g,r.table_select[0],5),14==r.table_select[1]&&(r.table_select[1]=16),l(g,r.table_select[1],5),14==r.table_select[2]&&(r.table_select[2]=16),l(g,r.table_select[2],5),l(g,r.region0_count,4),l(g,r.region1_count,3));l(g,r.preflag,1);l(g,r.scalefac_scale,1);l(g,r.count1table_select,
1)}}else{l(g,f.main_data_begin,8);l(g,f.private_bits,g.channels_out);for(k=y=0;k<g.channels_out;k++)r=f.tt[y][k],l(g,r.part2_3_length+r.part2_length,12),l(g,r.big_values/2,9),l(g,r.global_gain,8),l(g,r.scalefac_compress,9),r.block_type!=e.NORM_TYPE?(l(g,1,1),l(g,r.block_type,2),l(g,r.mixed_block_flag,1),14==r.table_select[0]&&(r.table_select[0]=16),l(g,r.table_select[0],5),14==r.table_select[1]&&(r.table_select[1]=16),l(g,r.table_select[1],5),l(g,r.subblock_gain[0],3),l(g,r.subblock_gain[1],3),l(g,
r.subblock_gain[2],3)):(l(g,0,1),14==r.table_select[0]&&(r.table_select[0]=16),l(g,r.table_select[0],5),14==r.table_select[1]&&(r.table_select[1]=16),l(g,r.table_select[1],5),14==r.table_select[2]&&(r.table_select[2]=16),l(g,r.table_select[2],5),l(g,r.region0_count,4),l(g,r.region1_count,3)),l(g,r.scalefac_scale,1),l(g,r.count1table_select,1)}a.error_protection&&CRC_writeheader(g,g.header[g.h_ptr].buf);f=g.h_ptr;g.h_ptr=f+1&aa.MAX_HEADER_BUF-1;g.header[g.h_ptr].write_timing=g.header[f].write_timing+
m;g.h_ptr==g.w_ptr&&N.err.println("Error: MAX_HEADER_BUF too small in bitstream.c \n");var g=8*c.sideinfo_len,u=0,C=a.internal_flags,J=C.l3_side;if(1==a.version)for(f=0;2>f;f++)for(k=0;k<C.channels_out;k++){var R=J.tt[f][k],z=La.slen1_tab[R.scalefac_compress],O=La.slen2_tab[R.scalefac_compress];for(y=r=0;y<R.sfbdivide;y++)-1!=R.scalefac[y]&&(D(C,R.scalefac[y],z),r+=z);for(;y<R.sfbmax;y++)-1!=R.scalefac[y]&&(D(C,R.scalefac[y],O),r+=O);r=R.block_type==e.SHORT_TYPE?r+q(C,R):r+h(C,R);r+=x(C,R);u+=r}else for(k=
f=0;k<C.channels_out;k++){var R=J.tt[f][k],V=0,O=y=r=0;if(R.block_type==e.SHORT_TYPE){for(;4>O;O++)for(var da=R.sfb_partition_table[O]/3,ga=R.slen[O],z=0;z<da;z++,y++)D(C,Math.max(R.scalefac[3*y+0],0),ga),D(C,Math.max(R.scalefac[3*y+1],0),ga),D(C,Math.max(R.scalefac[3*y+2],0),ga),V+=3*ga;r+=q(C,R)}else{for(;4>O;O++){da=R.sfb_partition_table[O];ga=R.slen[O];for(z=0;z<da;z++,y++)D(C,Math.max(R.scalefac[y],0),ga),V+=ga}r+=h(C,R)}r+=x(C,R);u+=V+r}g+=u;p(a,d.resvDrain_post);g+=d.resvDrain_post;d.main_data_begin+=
(m-g)/8;n(a,new b)!=c.ResvSize&&N.err.println("Internal buffer inconsistency. flushbits <> ResvSize");8*d.main_data_begin!=c.ResvSize&&(N.err.printf("bit reservoir error: \nl3_side.main_data_begin: %d \nResvoir size:             %d \nresv drain (post)         %d \nresv drain (pre)          %d \nheader and sideinfo:      %d \ndata bits:                %d \ntotal bits:               %d (remainder: %d) \nbitsperframe:             %d \n",8*d.main_data_begin,c.ResvSize,d.resvDrain_post,d.resvDrain_pre,
8*c.sideinfo_len,g-d.resvDrain_post-8*c.sideinfo_len,g,g%8,m),N.err.println("This is a fatal error.  It has several possible causes:"),N.err.println("90%%  LAME compiled with buggy version of gcc using advanced optimizations"),N.err.println(" 9%%  Your system is overclocked"),N.err.println(" 1%%  bug in LAME encoding library"),c.ResvSize=8*d.main_data_begin);if(1E9<B){for(a=0;a<aa.MAX_HEADER_BUF;++a)c.header[a].write_timing-=B;B=0}return 0};this.copy_buffer=function(a,b,e,m,g){var f=y+1;if(0>=f)return 0;
if(0!=m&&f>m)return-1;N.arraycopy(c,0,b,e,f);y=-1;r=0;if(0!=g&&(m=T(1),m[0]=a.nMusicCRC,d.updateMusicCRC(m,b,e,f),a.nMusicCRC=m[0],0<f&&(a.VBR_seek_table.nBytesWritten+=f),a.decode_on_the_fly))for(var m=qa([2,1152]),g=f,B=-1,h;0!=B;)if(B=k.hip_decode1_unclipped(a.hip,b,e,g,m[0],m[1]),g=0,-1==B&&(B=0),0<B){if(a.findPeakSample){for(h=0;h<B;h++)m[0][h]>a.PeakSample?a.PeakSample=m[0][h]:-m[0][h]>a.PeakSample&&(a.PeakSample=-m[0][h]);if(1<a.channels_out)for(h=0;h<B;h++)m[1][h]>a.PeakSample?a.PeakSample=
m[1][h]:-m[1][h]>a.PeakSample&&(a.PeakSample=-m[1][h])}if(a.findReplayGain&&J.AnalyzeSamples(a.rgdata,m[0],0,m[1],0,B,a.channels_out)==U.GAIN_ANALYSIS_ERROR)return-6}return f};this.init_bit_stream_w=function(a){c=new Int8Array(E.LAME_MAXMP3BUFFER);a.h_ptr=a.w_ptr=0;a.header[a.h_ptr].write_timing=0;y=-1;B=r=0}}function Da(){function e(a,b){var d=a[b+0]&255,d=d<<8|a[b+1]&255,d=d<<8,d=d|a[b+2]&255,d=d<<8;return d|=a[b+3]&255}function p(a,b,d){a[b+0]=255&d>>24&255;a[b+1]=255&d>>16&255;a[b+2]=255&d>>8&
255;a[b+3]=255&d&255}function l(a,b,d){a[b+0]=255&d>>8&255;a[b+1]=255&d&255}function z(a,b,d){return 255&(a<<b|d&~(-1<<b))}function x(a,b){var d=a.internal_flags;b[0]=z(b[0],8,255);b[1]=z(b[1],3,7);b[1]=z(b[1],1,16E3>a.out_samplerate?0:1);b[1]=z(b[1],1,a.version);b[1]=z(b[1],2,1);b[1]=z(b[1],1,!a.error_protection?1:0);b[2]=z(b[2],4,d.bitrate_index);b[2]=z(b[2],2,d.samplerate_index);b[2]=z(b[2],1,0);b[2]=z(b[2],1,a.extension);b[3]=z(b[3],2,a.mode.ordinal());b[3]=z(b[3],2,d.mode_ext);b[3]=z(b[3],1,
a.copyright);b[3]=z(b[3],1,a.original);b[3]=z(b[3],2,a.emphasis);b[0]=255;var d=255&b[1]&241,e;e=1==a.version?J:16E3>a.out_samplerate?g:k;a.VBR==F.vbr_off&&(e=a.brate);e=a.free_format?0:255&16*q.BitrateIndex(e,a.version,a.out_samplerate);b[1]=1==a.version?255&(d|10):255&(d|2);d=255&b[2]&13;b[2]=255&(e|d)}function u(a,b){return b=b>>8^d[(b^a)&255]}var q,h,b;this.setModules=function(a,d,e){q=a;h=d;b=e};var n=Da.NUMTOCENTRIES,a=Da.MAXFRAMESIZE,f=n+4+4+4+4+4+9+1+1+8+1+1+3+1+1+2+4+2+2,J=128,k=64,g=32,
d=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8E3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16E3,65089,64001,15040,
15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32E3,48577,48257,
31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];this.addVbrFrame=
function(a){var b=a.internal_flags;var d=b.VBR_seek_table,a=v.bitrate_table[a.version][b.bitrate_index];d.nVbrNumFrames++;d.sum+=a;d.seen++;if(!(d.seen<d.want)&&(d.pos<d.size&&(d.bag[d.pos]=d.sum,d.pos++,d.seen=0),d.pos==d.size)){for(a=1;a<d.size;a+=2)d.bag[a/2]=d.bag[a];d.want*=2;d.pos/=2}};this.getVbrTag=function(a){var b=new VBRTagData,d=0;b.flags=0;var g=a[d+1]>>3&1,f=a[d+2]>>2&3,h=a[d+3]>>6&3,i=a[d+2]>>4&15,i=v.bitrate_table[g][i];b.samprate=14==a[d+1]>>4?v.samplerate_table[2][f]:v.samplerate_table[g][f];
d=0!=g?3!=h?d+36:d+21:3!=h?d+21:d+13;if(!(new String(a,d,4(),null)).equals("Xing")&&!(new String(a,d,4(),null)).equals("Info"))return null;d+=4;b.hId=g;f=b.flags=e(a,d);d+=4;0!=(f&1)&&(b.frames=e(a,d),d+=4);0!=(f&2)&&(b.bytes=e(a,d),d+=4);if(0!=(f&4)){if(null!=b.toc)for(h=0;h<n;h++)b.toc[h]=a[d+h];d+=n}b.vbrScale=-1;0!=(f&8)&&(b.vbrScale=e(a,d),d+=4);b.headersize=72E3*(g+1)*i/b.samprate;d+=21;g=a[d+0]<<4;g+=a[d+1]>>4;i=(a[d+1]&15)<<8;i+=a[d+2]&255;if(0>g||3E3<g)g=-1;if(0>i||3E3<i)i=-1;b.encDelay=
g;b.encPadding=i;return b};this.InitVbrTag=function(b){var d=b.internal_flags,e;e=1==b.version?J:16E3>b.out_samplerate?g:k;b.VBR==F.vbr_off&&(e=b.brate);e=72E3*(b.version+1)*e/b.out_samplerate;var r=d.sideinfo_len+f;d.VBR_seek_table.TotalFrameSize=e;if(e<r||e>a)b.bWriteVbrTag=!1;else{d.VBR_seek_table.nVbrNumFrames=0;d.VBR_seek_table.nBytesWritten=0;d.VBR_seek_table.sum=0;d.VBR_seek_table.seen=0;d.VBR_seek_table.want=1;d.VBR_seek_table.pos=0;null==d.VBR_seek_table.bag&&(d.VBR_seek_table.bag=new int[400],
d.VBR_seek_table.size=400);e=new Int8Array(a);x(b,e);d=d.VBR_seek_table.TotalFrameSize;for(r=0;r<d;++r)h.add_dummy_byte(b,e[r]&255,1)}};this.updateMusicCRC=function(a,b,d,e){for(var g=0;g<e;++g)a[0]=u(b[d+g],a[0])};this.getLameTagFrame=function(a,d){var e=a.internal_flags;if(!a.bWriteVbrTag||e.Class_ID!=E.LAME_ID||0>=e.VBR_seek_table.pos)return 0;if(d.length<e.VBR_seek_table.TotalFrameSize)return e.VBR_seek_table.TotalFrameSize;Ba.fill(d,0,e.VBR_seek_table.TotalFrameSize,0);x(a,d);var g=new Int8Array(n);
if(a.free_format)for(var f=1;f<n;++f)g[f]=255&255*f/100;else{var j=e.VBR_seek_table;if(!(0>=j.pos))for(f=1;f<n;++f){var i=0|Math.floor(f/n*j.pos);i>j.pos-1&&(i=j.pos-1);i=0|256*j.bag[i]/j.sum;255<i&&(i=255);g[f]=255&i}}i=e.sideinfo_len;a.error_protection&&(i-=2);d[i++]=0;d[i++]=0;d[i++]=0;d[i++]=0;p(d,i,15);i+=4;p(d,i,e.VBR_seek_table.nVbrNumFrames);i+=4;j=e.VBR_seek_table.nBytesWritten+e.VBR_seek_table.TotalFrameSize;p(d,i,0|j);i+=4;N.arraycopy(g,0,d,i,g.length);i+=g.length;a.error_protection&&h.CRC_writeheader(e,
d);for(var m=0,f=0;f<i;f++)m=u(d[f],m);var g=i,f=m,w=a.internal_flags,i=0,m=a.encoder_delay,t=a.encoder_padding,k=100-10*a.VBR_q-a.quality,q=b.getLameVeryShortVersion(),J;J=[1,5,3,2,4,0,3];var D=0|(255<a.lowpassfreq/100+0.5?255:a.lowpassfreq/100+0.5),C=0,z=0,R=a.internal_flags.noise_shaping,v=0,O=0,V=0,da=0,v=0,v=0!=(a.exp_nspsytune&1),V=0!=(a.exp_nspsytune&2),ga=!1,wc=!1,xc=a.internal_flags.nogap_total,yc=a.internal_flags.nogap_current,da=a.ATHtype,K=0,hc;switch(a.VBR){case vbr_abr:hc=a.VBR_mean_bitrate_kbps;
break;case vbr_off:hc=a.brate;break;default:hc=a.VBR_min_bitrate_kbps}J=0+(a.VBR.ordinal()<J.length?J[a.VBR.ordinal()]:0);w.findReplayGain&&(510<w.RadioGain&&(w.RadioGain=510),-510>w.RadioGain&&(w.RadioGain=-510),z=11264,z=0<=w.RadioGain?z|w.RadioGain:z|512|-w.RadioGain);w.findPeakSample&&(C=Math.abs(0|w.PeakSample/32767*Math.pow(2,23)+0.5));-1!=xc&&(0<yc&&(wc=!0),yc<xc-1&&(ga=!0));K=da+((v?1:0)<<4)+((V?1:0)<<5)+((ga?1:0)<<6)+((wc?1:0)<<7);0>k&&(k=0);switch(a.mode){case MONO:v=0;break;case STEREO:v=
1;break;case DUAL_CHANNEL:v=2;break;case JOINT_STEREO:v=a.force_ms?4:3;break;default:v=7}V=32E3>=a.in_samplerate?0:48E3==a.in_samplerate?2:48E3<a.in_samplerate?3:1;if(a.short_blocks==ya.short_block_forced||a.short_blocks==ya.short_block_dispensed||-1==a.lowpassfreq&&-1==a.highpassfreq||a.scale_left<a.scale_right||a.scale_left>a.scale_right||a.disable_reservoir&&320>a.brate||a.noATH||a.ATHonly||0==da||32E3>=a.in_samplerate)O=1;da=R+(v<<2)+(O<<5)+(V<<6);v=w.nMusicCRC;p(d,g+i,k);i+=4;for(w=0;9>w;w++)d[g+
i+w]=255&q.charAt(w);i+=9;d[g+i]=255&J;i++;d[g+i]=255&D;i++;p(d,g+i,C);i+=4;l(d,g+i,z);i+=2;l(d,g+i,0);i+=2;d[g+i]=255&K;i++;d[g+i]=255<=hc?255:255&hc;i++;d[g+i]=255&m>>4;d[g+i+1]=255&(m<<4)+(t>>8);d[g+i+2]=255&t;i+=3;d[g+i]=255&da;i++;d[g+i++]=0;l(d,g+i,a.preset);i+=2;p(d,g+i,j);i+=4;l(d,g+i,v);i+=2;for(j=0;j<i;j++)f=u(d[g+j],f);l(d,g+i,f);return e.VBR_seek_table.TotalFrameSize};this.putVbrTag=function(d,b){if(0>=d.internal_flags.VBR_seek_table.pos)return-1;b.seek(b.length());if(0==b.length())return-1;
b.seek(0);var g=new Int8Array(10);b.readFully(g);g=(new String(g,"ISO-8859-1")).startsWith("ID3")?0:((g[6]&127)<<21|(g[7]&127)<<14|(g[8]&127)<<7|g[9]&127)+g.length;b.seek(g);var g=new Int8Array(a),e=getLameTagFrame(d,g);if(e>g.length)return-1;if(1>e)return 0;b.write(g,0,e);return 0}}function W(e,p,l,z){this.xlen=e;this.linmax=p;this.table=l;this.hlen=z}function Ha(e){this.bits=e}function Dc(){this.setModules=function(){}}function bc(){this.bits=this.over_SSD=this.over_count=this.max_noise=this.tot_noise=
this.over_noise=0}function Ec(){this.scale_right=this.scale_left=this.scale=this.out_samplerate=this.in_samplerate=this.num_channels=this.num_samples=this.class_id=0;this.decode_only=this.bWriteVbrTag=this.analysis=!1;this.quality=0;this.mode=ia.STEREO;this.write_id3tag_automatic=this.decode_on_the_fly=this.findReplayGain=this.free_format=this.force_ms=!1;this.error_protection=this.emphasis=this.extension=this.original=this.copyright=this.compression_ratio=this.brate=0;this.disable_reservoir=this.strict_ISO=
!1;this.quant_comp_short=this.quant_comp=0;this.experimentalY=!1;this.preset=this.exp_nspsytune=this.experimentalZ=0;this.VBR=null;this.maskingadjust_short=this.maskingadjust=this.highpasswidth=this.lowpasswidth=this.highpassfreq=this.lowpassfreq=this.VBR_hard_min=this.VBR_max_bitrate_kbps=this.VBR_min_bitrate_kbps=this.VBR_mean_bitrate_kbps=this.VBR_q=this.VBR_q_frac=0;this.noATH=this.ATHshort=this.ATHonly=!1;this.athaa_sensitivity=this.athaa_loudapprox=this.athaa_type=this.ATHlower=this.ATHcurve=
this.ATHtype=0;this.short_blocks=null;this.useTemporal=!1;this.msfix=this.interChRatio=0;this.tune=!1;this.lame_allocated_gfp=this.frameNum=this.framesize=this.encoder_padding=this.encoder_delay=this.version=this.tune_value_a=0;this.internal_flags=null}function Fc(){this.floor=this.decay=this.adjustLimit=this.adjust=this.aaSensitivityP=this.useAdjust=0;this.l=K(e.SBMAX_l);this.s=K(e.SBMAX_s);this.psfb21=K(e.PSFB21);this.psfb12=K(e.PSFB12);this.cb_l=K(e.CBANDS);this.cb_s=K(e.CBANDS);this.eql_w=K(e.BLKSIZE/
2)}function Gc(){this.linprebuf=K(2*U.MAX_ORDER);this.linpre=0;this.lstepbuf=K(U.MAX_SAMPLES_PER_WINDOW+U.MAX_ORDER);this.lstep=0;this.loutbuf=K(U.MAX_SAMPLES_PER_WINDOW+U.MAX_ORDER);this.lout=0;this.rinprebuf=K(2*U.MAX_ORDER);this.rinpre=0;this.rstepbuf=K(U.MAX_SAMPLES_PER_WINDOW+U.MAX_ORDER);this.rstep=0;this.routbuf=K(U.MAX_SAMPLES_PER_WINDOW+U.MAX_ORDER);this.first=this.freqindex=this.rsum=this.lsum=this.totsamp=this.sampleWindow=this.rout=0;this.A=T(0|U.STEPS_per_dB*U.MAX_dB);this.B=T(0|U.STEPS_per_dB*
U.MAX_dB)}function Hc(D){this.quantize=D;this.iteration_loop=function(p,l,D,x){var u=p.internal_flags,q=K(ra.SFBMAX),h=K(576),b=T(2),n,a,f=u.l3_side;n=new Ha(0);this.quantize.rv.ResvFrameBegin(p,n);n=n.bits;for(var J=0;J<u.mode_gr;J++){a=this.quantize.qupvt.on_pe(p,l,b,n,J,J);u.mode_ext==e.MPG_MD_MS_LR&&(this.quantize.ms_convert(u.l3_side,J),this.quantize.qupvt.reduce_side(b,D[J],n,a));for(a=0;a<u.channels_out;a++){var k,g=f.tt[J][a];g.block_type!=e.SHORT_TYPE?(k=0,k=u.PSY.mask_adjust-k):(k=0,k=u.PSY.mask_adjust_short-
k);u.masking_lower=Math.pow(10,0.1*k);this.quantize.init_outer_loop(u,g);this.quantize.init_xrpow(u,g,h)&&(this.quantize.qupvt.calc_xmin(p,x[J][a],g,q),this.quantize.outer_loop(p,g,q,h,a,b[a]));this.quantize.iteration_finish_one(u,J,a)}}this.quantize.rv.ResvFrameEnd(u,n)}}function la(D,p,l,z){this.l=T(1+e.SBMAX_l);this.s=T(1+e.SBMAX_s);this.psfb21=T(1+e.PSFB21);this.psfb12=T(1+e.PSFB12);var x=this.l,u=this.s;4==arguments.length&&(this.arrL=arguments[0],this.arrS=arguments[1],this.arr21=arguments[2],
this.arr12=arguments[3],N.arraycopy(this.arrL,0,x,0,Math.min(this.arrL.length,this.l.length)),N.arraycopy(this.arrS,0,u,0,Math.min(this.arrS.length,this.s.length)),N.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),N.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function ca(){function D(a,d){var c=x.ATHformula(d,a),c=c-b;return c=Math.pow(10,c/10+a.ATHlower)}function p(a){this.s=a}var l=null,z=null,x=null;this.setModules=
function(a,d,b){l=a;z=d;x=b};this.IPOW20=function(a){return f[a]};var u=ca.IXMAX_VAL+2,q=ca.Q_MAX,h=ca.Q_MAX2,b=100;this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var n=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,0];this.pretab=n;this.sfBandIndex=[new la([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,
396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new la([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new la([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new la([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,
576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new la([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new la([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new la([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,
18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new la([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new la([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0])];var a=K(q+h+1),f=K(q),J=K(u),k=K(u);this.adj43=k;this.iteration_init=function(b){var d=b.internal_flags,
c=d.l3_side;if(0==d.iteration_init_init){d.iteration_init_init=1;c.main_data_begin=0;for(var c=b.internal_flags.ATH.l,n=b.internal_flags.ATH.psfb21,y=b.internal_flags.ATH.s,r=b.internal_flags.ATH.psfb12,s=b.internal_flags,j=b.out_samplerate,i=0;i<e.SBMAX_l;i++){var m=s.scalefac_band.l[i],w=s.scalefac_band.l[i+1];for(c[i]=Ma.MAX_VALUE;m<w;m++){var t=m*j/1152,t=D(b,t);c[i]=Math.min(c[i],t)}}for(i=0;i<e.PSFB21;i++){m=s.scalefac_band.psfb21[i];w=s.scalefac_band.psfb21[i+1];for(n[i]=Ma.MAX_VALUE;m<w;m++)t=
m*j/1152,t=D(b,t),n[i]=Math.min(n[i],t)}for(i=0;i<e.SBMAX_s;i++){m=s.scalefac_band.s[i];w=s.scalefac_band.s[i+1];for(y[i]=Ma.MAX_VALUE;m<w;m++)t=m*j/384,t=D(b,t),y[i]=Math.min(y[i],t);y[i]*=s.scalefac_band.s[i+1]-s.scalefac_band.s[i]}for(i=0;i<e.PSFB12;i++){m=s.scalefac_band.psfb12[i];w=s.scalefac_band.psfb12[i+1];for(r[i]=Ma.MAX_VALUE;m<w;m++)t=m*j/384,t=D(b,t),r[i]=Math.min(r[i],t);r[i]*=s.scalefac_band.s[13]-s.scalefac_band.s[12]}if(b.noATH){for(i=0;i<e.SBMAX_l;i++)c[i]=1E-20;for(i=0;i<e.PSFB21;i++)n[i]=
1E-20;for(i=0;i<e.SBMAX_s;i++)y[i]=1E-20;for(i=0;i<e.PSFB12;i++)r[i]=1E-20}s.ATH.floor=10*Math.log10(D(b,-1));J[0]=0;for(c=1;c<u;c++)J[c]=Math.pow(c,4/3);for(c=0;c<u-1;c++)k[c]=c+1-Math.pow(0.5*(J[c]+J[c+1]),0.75);k[c]=0.5;for(c=0;c<q;c++)f[c]=Math.pow(2,-0.1875*(c-210));for(c=0;c<=q+h;c++)a[c]=Math.pow(2,0.25*(c-210-h));l.huffman_init(d);c=b.exp_nspsytune>>2&63;32<=c&&(c-=64);n=Math.pow(10,c/4/10);c=b.exp_nspsytune>>8&63;32<=c&&(c-=64);y=Math.pow(10,c/4/10);c=b.exp_nspsytune>>14&63;32<=c&&(c-=64);
r=Math.pow(10,c/4/10);c=b.exp_nspsytune>>20&63;32<=c&&(c-=64);b=r*Math.pow(10,c/4/10);for(c=0;c<e.SBMAX_l;c++)s=6>=c?n:13>=c?y:20>=c?r:b,d.nsPsy.longfact[c]=s;for(c=0;c<e.SBMAX_s;c++)s=5>=c?n:10>=c?y:11>=c?r:b,d.nsPsy.shortfact[c]=s}};this.on_pe=function(a,b,c,e,f,h){var n=a.internal_flags,j=0,i=T(2),m,j=new Ha(j),a=z.ResvMaxBits(a,e,j,h),j=j.bits,w=j+a;w>aa.MAX_BITS_PER_GRANULE&&(w=aa.MAX_BITS_PER_GRANULE);for(m=h=0;m<n.channels_out;++m)c[m]=Math.min(aa.MAX_BITS_PER_CHANNEL,j/n.channels_out),i[m]=
0|c[m]*b[f][m]/700-c[m],i[m]>3*e/4&&(i[m]=3*e/4),0>i[m]&&(i[m]=0),i[m]+c[m]>aa.MAX_BITS_PER_CHANNEL&&(i[m]=Math.max(0,aa.MAX_BITS_PER_CHANNEL-c[m])),h+=i[m];if(h>a)for(m=0;m<n.channels_out;++m)i[m]=a*i[m]/h;for(m=0;m<n.channels_out;++m)c[m]+=i[m],a-=i[m];for(m=h=0;m<n.channels_out;++m)h+=c[m];if(h>aa.MAX_BITS_PER_GRANULE)for(m=0;m<n.channels_out;++m)c[m]*=aa.MAX_BITS_PER_GRANULE,c[m]/=h;return w};this.reduce_side=function(a,b,c,e){b=0.33*(0.5-b)/0.5;0>b&&(b=0);0.5<b&&(b=0.5);b=0|0.5*b*(a[0]+a[1]);
b>aa.MAX_BITS_PER_CHANNEL-a[0]&&(b=aa.MAX_BITS_PER_CHANNEL-a[0]);0>b&&(b=0);125<=a[1]&&(125<a[1]-b?(a[0]<c&&(a[0]+=b),a[1]-=b):(a[0]+=a[1]-125,a[1]=125));b=a[0]+a[1];b>e&&(a[0]=e*a[0]/b,a[1]=e*a[1]/b)};this.athAdjust=function(a,b,c){var b=X.FAST_LOG10_X(b,10),a=a*a,e=0,b=b-c;1E-20<a&&(e=1+X.FAST_LOG10_X(a,10/90.30873362));0>e&&(e=0);b=b*e+(c+90.30873362-94.82444863);return Math.pow(10,0.1*b)};this.calc_xmin=function(a,b,c,f){var h=0,n=a.internal_flags,k,j=0,i=0,m=n.ATH,w=c.xr,t=a.VBR==F.vbr_mtrh?
1:0,l=n.masking_lower;if(a.VBR==F.vbr_mtrh||a.VBR==F.vbr_mt)l=1;for(k=0;k<c.psy_lmax;k++){var p,q,u,C,J,D;q=a.VBR==F.vbr_rh||a.VBR==F.vbr_mtrh?athAdjust(m.adjust,m.l[k],m.floor):m.adjust*m.l[k];J=c.width[k];u=q/J;C=2.220446049250313E-16;D=J>>1;p=0;do{var x;x=w[j]*w[j];p+=x;C+=x<u?x:u;j++;x=w[j]*w[j];p+=x;C+=x<u?x:u;j++}while(0<--D);p>q&&i++;k==e.SBPSY_l&&(u=q*n.nsPsy.longfact[k],C<u&&(C=u));0!=t&&(q=C);a.ATHonly||(C=b.en.l[k],0<C&&(u=p*b.thm.l[k]*l/C,0!=t&&(u*=n.nsPsy.longfact[k]),q<u&&(q=u)));0!=
t?f[h++]=q:f[h++]=q*n.nsPsy.longfact[k]}p=575;if(c.block_type!=e.SHORT_TYPE)for(q=576;0!=q--&&ka.EQ(w[q],0);)p=q;c.max_nonzero_coeff=p;for(var O=c.sfb_smin;k<c.psymax;O++,k+=3){var V,da;da=a.VBR==F.vbr_rh||a.VBR==F.vbr_mtrh?athAdjust(m.adjust,m.s[O],m.floor):m.adjust*m.s[O];J=c.width[k];for(V=0;3>V;V++){p=0;D=J>>1;u=da/J;C=2.220446049250313E-16;do x=w[j]*w[j],p+=x,C+=x<u?x:u,j++,x=w[j]*w[j],p+=x,C+=x<u?x:u,j++;while(0<--D);p>da&&i++;O==e.SBPSY_s&&(u=da*n.nsPsy.shortfact[O],C<u&&(C=u));q=0!=t?C:da;
!a.ATHonly&&!a.ATHshort&&(C=b.en.s[O][V],0<C&&(u=p*b.thm.s[O][V]*l/C,0!=t&&(u*=n.nsPsy.shortfact[O]),q<u&&(q=u)));0!=t?f[h++]=q:f[h++]=q*n.nsPsy.shortfact[O]}a.useTemporal&&(f[h-3]>f[h-3+1]&&(f[h-3+1]+=(f[h-3]-f[h-3+1])*n.decay),f[h-3+1]>f[h-3+2]&&(f[h-3+2]+=(f[h-3+1]-f[h-3+2])*n.decay))}return i};this.calc_noise_core=function(a,b,c,e){var f=0,h=b.s,n=a.l3_enc;if(h>a.count1)for(;0!=c--;){var k;k=a.xr[h];h++;f+=k*k;k=a.xr[h];h++;f+=k*k}else if(h>a.big_values){var i=K(2);i[0]=0;for(i[1]=e;0!=c--;)k=
Math.abs(a.xr[h])-i[n[h]],h++,f+=k*k,k=Math.abs(a.xr[h])-i[n[h]],h++,f+=k*k}else for(;0!=c--;)k=Math.abs(a.xr[h])-J[n[h]]*e,h++,f+=k*k,k=Math.abs(a.xr[h])-J[n[h]]*e,h++,f+=k*k;b.s=h;return f};this.calc_noise=function(b,d,c,e,f){var h=0,k=0,j,i,m=0,w=0,t=0,l=-20,q=0,u=b.scalefac,J=0;for(j=e.over_SSD=0;j<b.psymax;j++){var C=b.global_gain-(u[J++]+(0!=b.preflag?n[j]:0)<<b.scalefac_scale+1)-8*b.subblock_gain[b.window[j]],D=0;null!=f&&f.step[j]==C?(D=f.noise[j],q+=b.width[j],c[h++]=D/d[k++],D=f.noise_log[j]):
(D=a[C+ca.Q_MAX2],i=b.width[j]>>1,q+b.width[j]>b.max_nonzero_coeff&&(i=b.max_nonzero_coeff-q+1,i=0<i?i>>1:0),q=new p(q),D=this.calc_noise_core(b,q,i,D),q=q.s,null!=f&&(f.step[j]=C,f.noise[j]=D),D=c[h++]=D/d[k++],D=X.FAST_LOG10(Math.max(D,1E-20)),null!=f&&(f.noise_log[j]=D));null!=f&&(f.global_gain=b.global_gain);t+=D;0<D&&(C=Math.max(0|10*D+0.5,1),e.over_SSD+=C*C,m++,w+=D);l=Math.max(l,D)}e.over_count=m;e.tot_noise=t;e.over_noise=w;e.max_noise=l;return m};this.set_pinfo=function(a,b,c,f,h){var k=
a.internal_flags,l,j,i,m,w=0==b.scalefac_scale?0.5:1,t=b.scalefac,q=K(ra.SFBMAX),p=K(ra.SFBMAX),u=new bc;calc_xmin(a,c,b,q);calc_noise(b,q,p,u,null);var D=0;j=b.sfb_lmax;b.block_type!=e.SHORT_TYPE&&0==b.mixed_block_flag&&(j=22);for(l=0;l<j;l++){var J=k.scalefac_band.l[l],x=k.scalefac_band.l[l+1],z=x-J;for(m=0;D<x;D++)m+=b.xr[D]*b.xr[D];m/=z;i=1E15;k.pinfo.en[f][h][l]=i*m;k.pinfo.xfsf[f][h][l]=i*q[l]*p[l]/z;m=0<c.en.l[l]&&!a.ATHonly?m/c.en.l[l]:0;k.pinfo.thr[f][h][l]=i*Math.max(m*c.thm.l[l],k.ATH.l[l]);
k.pinfo.LAMEsfb[f][h][l]=0;0!=b.preflag&&11<=l&&(k.pinfo.LAMEsfb[f][h][l]=-w*n[l]);l<e.SBPSY_l&&(k.pinfo.LAMEsfb[f][h][l]-=w*t[l])}if(b.block_type==e.SHORT_TYPE){j=l;for(l=b.sfb_smin;l<e.SBMAX_s;l++)for(var J=k.scalefac_band.s[l],x=k.scalefac_band.s[l+1],z=x-J,v=0;3>v;v++){m=0;for(i=J;i<x;i++)m+=b.xr[D]*b.xr[D],D++;m=Math.max(m/z,1E-20);i=1E15;k.pinfo.en_s[f][h][3*l+v]=i*m;k.pinfo.xfsf_s[f][h][3*l+v]=i*q[j]*p[j]/z;m=0<c.en.s[l][v]?m/c.en.s[l][v]:0;if(a.ATHonly||a.ATHshort)m=0;k.pinfo.thr_s[f][h][3*
l+v]=i*Math.max(m*c.thm.s[l][v],k.ATH.s[l]);k.pinfo.LAMEsfb_s[f][h][3*l+v]=-2*b.subblock_gain[v];l<e.SBPSY_s&&(k.pinfo.LAMEsfb_s[f][h][3*l+v]-=w*t[j]);j++}}k.pinfo.LAMEqss[f][h]=b.global_gain;k.pinfo.LAMEmainbits[f][h]=b.part2_3_length+b.part2_length;k.pinfo.LAMEsfbits[f][h]=b.part2_length;k.pinfo.over[f][h]=u.over_count;k.pinfo.max_noise[f][h]=10*u.max_noise;k.pinfo.over_noise[f][h]=10*u.over_noise;k.pinfo.tot_noise[f][h]=10*u.tot_noise;k.pinfo.over_SSD[f][h]=u.over_SSD}}function gb(){this.xr=K(576);
this.l3_enc=T(576);this.scalefac=T(ra.SFBMAX);this.mixed_block_flag=this.block_type=this.scalefac_compress=this.global_gain=this.count1=this.big_values=this.part2_3_length=this.xrpow_max=0;this.table_select=T(3);this.subblock_gain=T(4);this.sfbdivide=this.psymax=this.sfbmax=this.psy_lmax=this.sfb_smin=this.sfb_lmax=this.part2_length=this.count1table_select=this.scalefac_scale=this.preflag=this.region1_count=this.region0_count=0;this.width=T(ra.SFBMAX);this.window=T(ra.SFBMAX);this.count1bits=0;this.sfb_partition_table=
null;this.slen=T(4);this.max_nonzero_coeff=0;var e=this;this.assign=function(p){e.xr=new Float32Array(p.xr);e.l3_enc=new Int32Array(p.l3_enc);e.scalefac=new Int32Array(p.scalefac);e.xrpow_max=p.xrpow_max;e.part2_3_length=p.part2_3_length;e.big_values=p.big_values;e.count1=p.count1;e.global_gain=p.global_gain;e.scalefac_compress=p.scalefac_compress;e.block_type=p.block_type;e.mixed_block_flag=p.mixed_block_flag;e.table_select=new Int32Array(p.table_select);e.subblock_gain=new Int32Array(p.subblock_gain);
e.region0_count=p.region0_count;e.region1_count=p.region1_count;e.preflag=p.preflag;e.scalefac_scale=p.scalefac_scale;e.count1table_select=p.count1table_select;e.part2_length=p.part2_length;e.sfb_lmax=p.sfb_lmax;e.sfb_smin=p.sfb_smin;e.psy_lmax=p.psy_lmax;e.sfbmax=p.sfbmax;e.psymax=p.psymax;e.sfbdivide=p.sfbdivide;e.width=new Int32Array(p.width);e.window=new Int32Array(p.window);e.count1bits=p.count1bits;e.sfb_partition_table=p.sfb_partition_table.slice(0);e.slen=new Int32Array(p.slen);e.max_nonzero_coeff=
p.max_nonzero_coeff}}function Ic(){this.sfb_count1=this.global_gain=0;this.step=T(39);this.noise=K(39);this.noise_log=K(39)}function Jc(){function D(e){this.ordinal=e}function p(e){for(var b=0;b<e.sfbmax;b++)if(0==e.scalefac[b]+e.subblock_gain[e.window[b]])return!1;return!0}var l;this.rv=null;var z;this.qupvt=null;var x,u=new Dc,q;this.setModules=function(e,b,n,a){l=e;this.rv=z=b;this.qupvt=x=n;q=a;u.setModules(x,q)};this.ms_convert=function(e,b){for(var n=0;576>n;++n){var a=e.tt[b][0].xr[n],f=e.tt[b][1].xr[n];
e.tt[b][0].xr[n]=(a+f)*0.5*X.SQRT2;e.tt[b][1].xr[n]=(a-f)*0.5*X.SQRT2}};this.init_xrpow=function(e,b,n){var a=0,f=0|b.max_nonzero_coeff;b.xrpow_max=0;Ba.fill(n,f,576,0);for(var l=a=0;l<=f;++l){var k=Math.abs(b.xr[l]),a=a+k;n[l]=Math.sqrt(k*Math.sqrt(k));n[l]>b.xrpow_max&&(b.xrpow_max=n[l])}if(1E-20<a){n=0;0!=(e.substep_shaping&2)&&(n=1);for(f=0;f<b.psymax;f++)e.pseudohalf[f]=n;return!0}Ba.fill(b.l3_enc,0,576,0);return!1};this.init_outer_loop=function(h,b){b.part2_3_length=0;b.big_values=0;b.count1=
0;b.global_gain=210;b.scalefac_compress=0;b.table_select[0]=0;b.table_select[1]=0;b.table_select[2]=0;b.subblock_gain[0]=0;b.subblock_gain[1]=0;b.subblock_gain[2]=0;b.subblock_gain[3]=0;b.region0_count=0;b.region1_count=0;b.preflag=0;b.scalefac_scale=0;b.count1table_select=0;b.part2_length=0;b.sfb_lmax=e.SBPSY_l;b.sfb_smin=e.SBPSY_s;b.psy_lmax=h.sfb21_extra?e.SBMAX_l:e.SBPSY_l;b.psymax=b.psy_lmax;b.sfbmax=b.sfb_lmax;b.sfbdivide=11;for(var n=0;n<e.SBMAX_l;n++)b.width[n]=h.scalefac_band.l[n+1]-h.scalefac_band.l[n],
b.window[n]=3;if(b.block_type==e.SHORT_TYPE){var a=K(576);b.sfb_smin=0;b.sfb_lmax=0;0!=b.mixed_block_flag&&(b.sfb_smin=3,b.sfb_lmax=2*h.mode_gr+4);b.psymax=b.sfb_lmax+3*((h.sfb21_extra?e.SBMAX_s:e.SBPSY_s)-b.sfb_smin);b.sfbmax=b.sfb_lmax+3*(e.SBPSY_s-b.sfb_smin);b.sfbdivide=b.sfbmax-18;b.psy_lmax=b.sfb_lmax;var f=h.scalefac_band.l[b.sfb_lmax];N.arraycopy(b.xr,0,a,0,576);for(n=b.sfb_smin;n<e.SBMAX_s;n++)for(var l=h.scalefac_band.s[n],k=h.scalefac_band.s[n+1],g=0;3>g;g++)for(var d=l;d<k;d++)b.xr[f++]=
a[3*d+g];a=b.sfb_lmax;for(n=b.sfb_smin;n<e.SBMAX_s;n++)b.width[a]=b.width[a+1]=b.width[a+2]=h.scalefac_band.s[n+1]-h.scalefac_band.s[n],b.window[a]=0,b.window[a+1]=1,b.window[a+2]=2,a+=3}b.count1bits=0;b.sfb_partition_table=x.nr_of_sfb_block[0][0];b.slen[0]=0;b.slen[1]=0;b.slen[2]=0;b.slen[3]=0;b.max_nonzero_coeff=575;Ba.fill(b.scalefac,0);n=h.ATH;a=b.xr;if(b.block_type!=e.SHORT_TYPE){f=!1;for(l=e.PSFB21-1;0<=l&&!f;l--){k=h.scalefac_band.psfb21[l];g=h.scalefac_band.psfb21[l+1];d=x.athAdjust(n.adjust,
n.psfb21[l],n.floor);1E-12<h.nsPsy.longfact[21]&&(d*=h.nsPsy.longfact[21]);for(g-=1;g>=k;g--)if(Math.abs(a[g])<d)a[g]=0;else{f=!0;break}}}else for(d=0;3>d;d++){f=!1;for(l=e.PSFB12-1;0<=l&&!f;l--){var k=3*h.scalefac_band.s[12]+(h.scalefac_band.s[13]-h.scalefac_band.s[12])*d+(h.scalefac_band.psfb12[l]-h.scalefac_band.psfb12[0]),g=k+(h.scalefac_band.psfb12[l+1]-h.scalefac_band.psfb12[l]),c=x.athAdjust(n.adjust,n.psfb12[l],n.floor);1E-12<h.nsPsy.shortfact[12]&&(c*=h.nsPsy.shortfact[12]);for(g-=1;g>=k;g--)if(Math.abs(a[g])<
c)a[g]=0;else{f=!0;break}}}};D.BINSEARCH_NONE=new D(0);D.BINSEARCH_UP=new D(1);D.BINSEARCH_DOWN=new D(2);this.trancate_smallspectrums=function(h,b,n,a){var f=K(ra.SFBMAX);if(!(0==(h.substep_shaping&4)&&b.block_type==e.SHORT_TYPE||0!=(h.substep_shaping&128))){x.calc_noise(b,n,f,new bc,null);for(var l=0;576>l;l++){var k=0;0!=b.l3_enc[l]&&(k=Math.abs(b.xr[l]));a[l]=k}l=0;k=8;b.block_type==e.SHORT_TYPE&&(k=6);do{var g,d,c,p,u=b.width[k],l=l+u;if(!(1<=f[k])&&(Ba.sort(a,l-u,u),!ka.EQ(a[l-1],0))){g=(1-f[k])*
n[k];p=d=0;do{var r;for(c=1;p+c<u&&!ka.NEQ(a[p+l-u],a[p+l+c-u]);c++);r=a[p+l-u]*a[p+l-u]*c;if(g<r){0!=p&&(d=a[p+l-u-1]);break}g-=r;p+=c}while(p<u);if(!ka.EQ(d,0)){do Math.abs(b.xr[l-u])<=d&&(b.l3_enc[l-u]=0);while(0<--u)}}}while(++k<b.psymax);b.part2_3_length=q.noquant_count_bits(h,b,null)}};this.outer_loop=function(h,b,n,a,f,l){var k=h.internal_flags,g=new gb,d=K(576),c=K(ra.SFBMAX),u=new bc,y,r=new Ic,s=9999999,j=!1,i=!1,m=0,w,t,A=k.CurrentStep[f],G=!1;y=k.OldValue[f];var z=D.BINSEARCH_NONE;b.global_gain=
y;for(w=l-b.part2_length;;){var v;t=q.count_bits(k,a,b,null);if(1==A||t==w)break;t>w?(z==D.BINSEARCH_DOWN&&(G=!0),G&&(A/=2),z=D.BINSEARCH_UP,v=A):(z==D.BINSEARCH_UP&&(G=!0),G&&(A/=2),z=D.BINSEARCH_DOWN,v=-A);b.global_gain+=v;0>b.global_gain&&(b.global_gain=0,G=!0);255<b.global_gain&&(b.global_gain=255,G=!0)}for(;t>w&&255>b.global_gain;)b.global_gain++,t=q.count_bits(k,a,b,null);k.CurrentStep[f]=4<=y-b.global_gain?4:2;k.OldValue[f]=b.global_gain;b.part2_3_length=t;if(0==k.noise_shaping)return 100;
x.calc_noise(b,n,c,u,r);u.bits=b.part2_3_length;g.assign(b);f=0;for(N.arraycopy(a,0,d,0,576);!j;){do{w=new bc;A=255;t=0!=(k.substep_shaping&2)?20:3;if(k.sfb21_extra){if(1<c[g.sfbmax])break;if(g.block_type==e.SHORT_TYPE&&(1<c[g.sfbmax+1]||1<c[g.sfbmax+2]))break}G=g;z=a;y=h.internal_flags;v=G;for(var C=c,L=z,R=h.internal_flags,E=void 0,E=0==v.scalefac_scale?1.2968395546510096:1.6817928305074292,O=0,V=0;V<v.sfbmax;V++)O<C[V]&&(O=C[V]);V=R.noise_shaping_amp;3==V&&(V=i?2:1);switch(V){case 2:break;case 1:O=
1<O?Math.pow(O,0.5):0.95*O;break;default:O=1<O?1:0.95*O}for(var da=0,V=0;V<v.sfbmax;V++){var ga=v.width[V],da=da+ga;if(!(C[V]<O)){if(0!=(R.substep_shaping&2)&&(R.pseudohalf[V]=0==R.pseudohalf[V]?1:0,0==R.pseudohalf[V]&&2==R.noise_shaping_amp))break;v.scalefac[V]++;for(ga=-ga;0>ga;ga++)L[da+ga]*=E,L[da+ga]>v.xrpow_max&&(v.xrpow_max=L[da+ga]);if(2==R.noise_shaping_amp)break}}if(v=p(G))G=!1;else if(v=2==y.mode_gr?q.scale_bitcount(G):q.scale_bitcount_lsf(y,G)){if(1<y.noise_shaping)if(Ba.fill(y.pseudohalf,
0),0==G.scalefac_scale){v=G;for(L=C=0;L<v.sfbmax;L++){E=v.width[L];R=v.scalefac[L];0!=v.preflag&&(R+=x.pretab[L]);C+=E;if(0!=(R&1)){R++;for(E=-E;0>E;E++)z[C+E]*=1.2968395546510096,z[C+E]>v.xrpow_max&&(v.xrpow_max=z[C+E])}v.scalefac[L]=R>>1}v.preflag=0;v.scalefac_scale=1;v=!1}else if(G.block_type==e.SHORT_TYPE&&0<y.subblock_gain){b:{v=y;C=G;L=void 0;R=C.scalefac;for(L=0;L<C.sfb_lmax;L++)if(16<=R[L]){z=!0;break b}for(E=0;3>E;E++){V=O=0;for(L=C.sfb_lmax+E;L<C.sfbdivide;L+=3)O<R[L]&&(O=R[L]);for(;L<C.sfbmax;L+=
3)V<R[L]&&(V=R[L]);if(!(16>O&&8>V)){if(7<=C.subblock_gain[E]){z=!0;break b}C.subblock_gain[E]++;O=v.scalefac_band.l[C.sfb_lmax];for(L=C.sfb_lmax+E;L<C.sfbmax;L+=3)if(V=C.width[L],da=R[L],da-=4>>C.scalefac_scale,0<=da)R[L]=da,O+=3*V;else{R[L]=0;da=x.IPOW20(210+(da<<C.scalefac_scale+1));O+=V*(E+1);for(ga=-V;0>ga;ga++)z[O+ga]*=da,z[O+ga]>C.xrpow_max&&(C.xrpow_max=z[O+ga]);O+=V*(3-E-1)}da=x.IPOW20(202);O+=C.width[L]*(E+1);for(ga=-C.width[L];0>ga;ga++)z[O+ga]*=da,z[O+ga]>C.xrpow_max&&(C.xrpow_max=z[O+
ga])}}z=!1}v=z||p(G)}v||(v=2==y.mode_gr?q.scale_bitcount(G):q.scale_bitcount_lsf(y,G));G=!v}else G=!0;if(!G)break;0!=g.scalefac_scale&&(A=254);G=l-g.part2_length;if(0>=G)break;for(;(g.part2_3_length=q.count_bits(k,a,g,r))>G&&g.global_gain<=A;)g.global_gain++;if(g.global_gain>A)break;if(0==u.over_count){for(;(g.part2_3_length=q.count_bits(k,a,g,r))>s&&g.global_gain<=A;)g.global_gain++;if(g.global_gain>A)break}x.calc_noise(g,n,c,w,r);w.bits=g.part2_3_length;y=b.block_type!=e.SHORT_TYPE?h.quant_comp:
h.quant_comp_short;A=u;G=w;z=g;v=c;C=void 0;switch(y){default:case 9:0<A.over_count?(C=G.over_SSD<=A.over_SSD,G.over_SSD==A.over_SSD&&(C=G.bits<A.bits)):C=0>G.max_noise&&10*G.max_noise+G.bits<=10*A.max_noise+A.bits;break;case 0:C=G.over_count<A.over_count||G.over_count==A.over_count&&G.over_noise<A.over_noise||G.over_count==A.over_count&&ka.EQ(G.over_noise,A.over_noise)&&G.tot_noise<A.tot_noise;break;case 8:y=G;C=1E-37;for(L=0;L<z.psymax;L++)C+=X.FAST_LOG10(0.368+0.632*v[L]*v[L]*v[L]);z=Math.max(1E-20,
C);y.max_noise=z;case 1:C=G.max_noise<A.max_noise;break;case 2:C=G.tot_noise<A.tot_noise;break;case 3:C=G.tot_noise<A.tot_noise&&G.max_noise<A.max_noise;break;case 4:C=0>=G.max_noise&&0.2<A.max_noise||0>=G.max_noise&&0>A.max_noise&&A.max_noise>G.max_noise-0.2&&G.tot_noise<A.tot_noise||0>=G.max_noise&&0<A.max_noise&&A.max_noise>G.max_noise-0.2&&G.tot_noise<A.tot_noise+A.over_noise||0<G.max_noise&&-0.05<A.max_noise&&A.max_noise>G.max_noise-0.1&&G.tot_noise+G.over_noise<A.tot_noise+A.over_noise||0<G.max_noise&&
-0.1<A.max_noise&&A.max_noise>G.max_noise-0.15&&G.tot_noise+G.over_noise+G.over_noise<A.tot_noise+A.over_noise+A.over_noise;break;case 5:C=G.over_noise<A.over_noise||ka.EQ(G.over_noise,A.over_noise)&&G.tot_noise<A.tot_noise;break;case 6:C=G.over_noise<A.over_noise||ka.EQ(G.over_noise,A.over_noise)&&(G.max_noise<A.max_noise||ka.EQ(G.max_noise,A.max_noise)&&G.tot_noise<=A.tot_noise);break;case 7:C=G.over_count<A.over_count||G.over_noise<A.over_noise}0==A.over_count&&(C=C&&G.bits<A.bits);y=C?1:0;if(0!=
y)s=b.part2_3_length,u=w,b.assign(g),f=0,N.arraycopy(a,0,d,0,576);else if(0==k.full_outer_loop){if(++f>t&&0==u.over_count)break;if(3==k.noise_shaping_amp&&i&&30<f)break;if(3==k.noise_shaping_amp&&i&&15<g.global_gain-m)break}}while(255>g.global_gain+g.scalefac_scale);3==k.noise_shaping_amp?i?j=!0:(g.assign(b),N.arraycopy(d,0,a,0,576),f=0,m=g.global_gain,i=!0):j=!0}h.VBR==F.vbr_rh||h.VBR==F.vbr_mtrh?N.arraycopy(d,0,a,0,576):0!=(k.substep_shaping&1)&&trancate_smallspectrums(k,b,n,a);return u.over_count};
this.iteration_finish_one=function(e,b,n){var a=e.l3_side,f=a.tt[b][n];q.best_scalefac_store(e,b,n,a);1==e.use_best_huffman&&q.best_huffman_divide(e,f);z.ResvAdjust(e,f)};this.VBR_encode_granule=function(e,b,n,a,f,l,k){var g=e.internal_flags,d=new gb,c=K(576),p=k,u=k+1,u=(k+l)/2,q,s=0,j=g.sfb21_extra;Ba.fill(d.l3_enc,0);do g.sfb21_extra=u>p-42?!1:j,q=outer_loop(e,b,n,a,f,u),0>=q?(s=1,u=b.part2_3_length,d.assign(b),N.arraycopy(a,0,c,0,576),k=u-32,q=k-l,u=(k+l)/2):(l=u+32,q=k-l,u=(k+l)/2,0!=s&&(s=2,
b.assign(d),N.arraycopy(c,0,a,0,576)));while(12<q);g.sfb21_extra=j;2==s&&N.arraycopy(d.l3_enc,0,b.l3_enc,0,576)};this.get_framebits=function(e,b){var n=e.internal_flags;n.bitrate_index=n.VBR_min_bitrate;var a=l.getframebits(e);n.bitrate_index=1;for(var a=l.getframebits(e),f=1;f<=n.VBR_max_bitrate;f++)n.bitrate_index=f,a=new Ha(a),b[f]=z.ResvFrameBegin(e,a),a=a.bits};this.VBR_old_prepare=function(h,b,n,a,f,l,k,g,d){var c=h.internal_flags,u;u=0;var p=1,q=0;c.bitrate_index=c.VBR_max_bitrate;var s=z.ResvFrameBegin(h,
new Ha(0))/c.mode_gr;get_framebits(h,l);for(var j=0;j<c.mode_gr;j++){var i=x.on_pe(h,b,g[j],s,j,0);c.mode_ext==e.MPG_MD_MS_LR&&(ms_convert(c.l3_side,j),x.reduce_side(g[j],n[j],s,i));for(i=0;i<c.channels_out;++i){var m=c.l3_side.tt[j][i];m.block_type!=e.SHORT_TYPE?(u=1.28/(1+Math.exp(3.5-b[j][i]/300))-0.05,u=c.PSY.mask_adjust-u):(u=2.56/(1+Math.exp(3.5-b[j][i]/300))-0.14,u=c.PSY.mask_adjust_short-u);c.masking_lower=Math.pow(10,0.1*u);init_outer_loop(c,m);d[j][i]=x.calc_xmin(h,a[j][i],m,f[j][i]);0!=
d[j][i]&&(p=0);k[j][i]=126;q+=g[j][i]}}for(j=0;j<c.mode_gr;j++)for(i=0;i<c.channels_out;i++)q>l[c.VBR_max_bitrate]&&(g[j][i]*=l[c.VBR_max_bitrate],g[j][i]/=q),k[j][i]>g[j][i]&&(k[j][i]=g[j][i]);return p};this.bitpressure_strategy=function(h,b,n,a){for(var f=0;f<h.mode_gr;f++)for(var l=0;l<h.channels_out;l++){for(var k=h.l3_side.tt[f][l],g=b[f][l],d=0,c=0;c<k.psy_lmax;c++)g[d++]*=1+0.029*c*c/e.SBMAX_l/e.SBMAX_l;if(k.block_type==e.SHORT_TYPE)for(c=k.sfb_smin;c<e.SBMAX_s;c++)g[d++]*=1+0.029*c*c/e.SBMAX_s/
e.SBMAX_s,g[d++]*=1+0.029*c*c/e.SBMAX_s/e.SBMAX_s,g[d++]*=1+0.029*c*c/e.SBMAX_s/e.SBMAX_s;a[f][l]=0|Math.max(n[f][l],0.9*a[f][l])}};this.VBR_new_prepare=function(h,b,l,a,f,u){var k=h.internal_flags,g=1,d=0,c=0,p;h.free_format?(k.bitrate_index=0,d=new Ha(d),p=z.ResvFrameBegin(h,d),d=d.bits,f[0]=p):(k.bitrate_index=k.VBR_max_bitrate,d=new Ha(d),z.ResvFrameBegin(h,d),d=d.bits,get_framebits(h,f),p=f[k.VBR_max_bitrate]);for(f=0;f<k.mode_gr;f++){x.on_pe(h,b,u[f],d,f,0);k.mode_ext==e.MPG_MD_MS_LR&&ms_convert(k.l3_side,
f);for(var q=0;q<k.channels_out;++q){var r=k.l3_side.tt[f][q];k.masking_lower=Math.pow(10,0.1*k.PSY.mask_adjust);init_outer_loop(k,r);0!=x.calc_xmin(h,l[f][q],r,a[f][q])&&(g=0);c+=u[f][q]}}for(f=0;f<k.mode_gr;f++)for(q=0;q<k.channels_out;q++)c>p&&(u[f][q]*=p,u[f][q]/=c);return g};this.calc_target_bits=function(h,b,n,a,f,u){var k=h.internal_flags,g=k.l3_side,d,c;k.bitrate_index=k.VBR_max_bitrate;c=new Ha(0);u[0]=z.ResvFrameBegin(h,c);k.bitrate_index=1;c=l.getframebits(h)-8*k.sideinfo_len;f[0]=c/(k.mode_gr*
k.channels_out);c=1E3*h.VBR_mean_bitrate_kbps*h.framesize;0!=(k.substep_shaping&1)&&(c*=1.09);c/=h.out_samplerate;c-=8*k.sideinfo_len;c/=k.mode_gr*k.channels_out;d=0.93+0.07*(11-h.compression_ratio)/5.5;0.9>d&&(d=0.9);1<d&&(d=1);for(h=0;h<k.mode_gr;h++){for(var q=0,f=0;f<k.channels_out;f++){a[h][f]=int(d*c);if(700<b[h][f]){var p=int((b[h][f]-700)/1.4),r=g.tt[h][f];a[h][f]=int(d*c);r.block_type==e.SHORT_TYPE&&p<c/2&&(p=c/2);p>3*c/2?p=3*c/2:0>p&&(p=0);a[h][f]+=p}a[h][f]>aa.MAX_BITS_PER_CHANNEL&&(a[h][f]=
aa.MAX_BITS_PER_CHANNEL);q+=a[h][f]}if(q>aa.MAX_BITS_PER_GRANULE)for(f=0;f<k.channels_out;++f)a[h][f]*=aa.MAX_BITS_PER_GRANULE,a[h][f]/=q}if(k.mode_ext==e.MPG_MD_MS_LR)for(h=0;h<k.mode_gr;h++)x.reduce_side(a[h],n[h],c*k.channels_out,aa.MAX_BITS_PER_GRANULE);for(h=b=0;h<k.mode_gr;h++)for(f=0;f<k.channels_out;f++)a[h][f]>aa.MAX_BITS_PER_CHANNEL&&(a[h][f]=aa.MAX_BITS_PER_CHANNEL),b+=a[h][f];if(b>u[0])for(h=0;h<k.mode_gr;h++)for(f=0;f<k.channels_out;f++)a[h][f]*=u[0],a[h][f]/=b}}function Kc(){function v(b,
e,a){for(var f=10,h=e+238-14-286,k=-15;0>k;k++){var g,d,c;g=p[f+-10];d=b[h+-224]*g;c=b[e+224]*g;g=p[f+-9];d+=b[h+-160]*g;c+=b[e+160]*g;g=p[f+-8];d+=b[h+-96]*g;c+=b[e+96]*g;g=p[f+-7];d+=b[h+-32]*g;c+=b[e+32]*g;g=p[f+-6];d+=b[h+32]*g;c+=b[e+-32]*g;g=p[f+-5];d+=b[h+96]*g;c+=b[e+-96]*g;g=p[f+-4];d+=b[h+160]*g;c+=b[e+-160]*g;g=p[f+-3];d+=b[h+224]*g;c+=b[e+-224]*g;g=p[f+-2];d+=b[e+-256]*g;c-=b[h+256]*g;g=p[f+-1];d+=b[e+-192]*g;c-=b[h+192]*g;g=p[f+0];d+=b[e+-128]*g;c-=b[h+128]*g;g=p[f+1];d+=b[e+-64]*g;c-=
b[h+64]*g;g=p[f+2];d+=b[e+0]*g;c-=b[h+0]*g;g=p[f+3];d+=b[e+64]*g;c-=b[h+-64]*g;g=p[f+4];d+=b[e+128]*g;c-=b[h+-128]*g;g=p[f+5];d+=b[e+192]*g;c-=b[h+-192]*g;d*=p[f+6];g=c-d;a[30+2*k]=c+d;a[31+2*k]=p[f+7]*g;f+=18;e--;h++}c=b[e+-16]*p[f+-10];d=b[e+-32]*p[f+-2];c+=(b[e+-48]-b[e+16])*p[f+-9];d+=b[e+-96]*p[f+-1];c+=(b[e+-80]+b[e+48])*p[f+-8];d+=b[e+-160]*p[f+0];c+=(b[e+-112]-b[e+80])*p[f+-7];d+=b[e+-224]*p[f+1];c+=(b[e+-144]+b[e+112])*p[f+-6];d-=b[e+32]*p[f+2];c+=(b[e+-176]-b[e+144])*p[f+-5];d-=b[e+96]*
p[f+3];c+=(b[e+-208]+b[e+176])*p[f+-4];d-=b[e+160]*p[f+4];c+=(b[e+-240]-b[e+208])*p[f+-3];d-=b[e+224];b=d-c;e=d+c;c=a[14];d=a[15]-c;a[31]=e+c;a[30]=b+d;a[15]=b-d;a[14]=e-c;d=a[28]-a[0];a[0]+=a[28];a[28]=d*p[f+-36+7];d=a[29]-a[1];a[1]+=a[29];a[29]=d*p[f+-36+7];d=a[26]-a[2];a[2]+=a[26];a[26]=d*p[f+-72+7];d=a[27]-a[3];a[3]+=a[27];a[27]=d*p[f+-72+7];d=a[24]-a[4];a[4]+=a[24];a[24]=d*p[f+-108+7];d=a[25]-a[5];a[5]+=a[25];a[25]=d*p[f+-108+7];d=a[22]-a[6];a[6]+=a[22];a[22]=d*X.SQRT2;d=a[23]-a[7];a[7]+=a[23];
a[23]=d*X.SQRT2-a[7];a[7]-=a[6];a[22]-=a[7];a[23]-=a[22];d=a[6];a[6]=a[31]-d;a[31]+=d;d=a[7];a[7]=a[30]-d;a[30]+=d;d=a[22];a[22]=a[15]-d;a[15]+=d;d=a[23];a[23]=a[14]-d;a[14]+=d;d=a[20]-a[8];a[8]+=a[20];a[20]=d*p[f+-180+7];d=a[21]-a[9];a[9]+=a[21];a[21]=d*p[f+-180+7];d=a[18]-a[10];a[10]+=a[18];a[18]=d*p[f+-216+7];d=a[19]-a[11];a[11]+=a[19];a[19]=d*p[f+-216+7];d=a[16]-a[12];a[12]+=a[16];a[16]=d*p[f+-252+7];d=a[17]-a[13];a[13]+=a[17];a[17]=d*p[f+-252+7];d=-a[20]+a[24];a[20]+=a[24];a[24]=d*p[f+-216+7];
d=-a[21]+a[25];a[21]+=a[25];a[25]=d*p[f+-216+7];d=a[4]-a[8];a[4]+=a[8];a[8]=d*p[f+-216+7];d=a[5]-a[9];a[5]+=a[9];a[9]=d*p[f+-216+7];d=a[0]-a[12];a[0]+=a[12];a[12]=d*p[f+-72+7];d=a[1]-a[13];a[1]+=a[13];a[13]=d*p[f+-72+7];d=a[16]-a[28];a[16]+=a[28];a[28]=d*p[f+-72+7];d=-a[17]+a[29];a[17]+=a[29];a[29]=d*p[f+-72+7];d=X.SQRT2*(a[2]-a[10]);a[2]+=a[10];a[10]=d;d=X.SQRT2*(a[3]-a[11]);a[3]+=a[11];a[11]=d;d=X.SQRT2*(-a[18]+a[26]);a[18]+=a[26];a[26]=d-a[18];d=X.SQRT2*(-a[19]+a[27]);a[19]+=a[27];a[27]=d-a[19];
d=a[2];a[19]-=a[3];a[3]-=d;a[2]=a[31]-d;a[31]+=d;d=a[3];a[11]-=a[19];a[18]-=d;a[3]=a[30]-d;a[30]+=d;d=a[18];a[27]-=a[11];a[19]-=d;a[18]=a[15]-d;a[15]+=d;d=a[19];a[10]-=d;a[19]=a[14]-d;a[14]+=d;d=a[10];a[11]-=d;a[10]=a[23]-d;a[23]+=d;d=a[11];a[26]-=d;a[11]=a[22]-d;a[22]+=d;d=a[26];a[27]-=d;a[26]=a[7]-d;a[7]+=d;d=a[27];a[27]=a[6]-d;a[6]+=d;d=X.SQRT2*(a[0]-a[4]);a[0]+=a[4];a[4]=d;d=X.SQRT2*(a[1]-a[5]);a[1]+=a[5];a[5]=d;d=X.SQRT2*(a[16]-a[20]);a[16]+=a[20];a[20]=d;d=X.SQRT2*(a[17]-a[21]);a[17]+=a[21];
a[21]=d;d=-X.SQRT2*(a[8]-a[12]);a[8]+=a[12];a[12]=d-a[8];d=-X.SQRT2*(a[9]-a[13]);a[9]+=a[13];a[13]=d-a[9];d=-X.SQRT2*(a[25]-a[29]);a[25]+=a[29];a[29]=d-a[25];d=-X.SQRT2*(a[24]+a[28]);a[24]-=a[28];a[28]=d-a[24];d=a[24]-a[16];a[24]=d;d=a[20]-d;a[20]=d;d=a[28]-d;a[28]=d;d=a[25]-a[17];a[25]=d;d=a[21]-d;a[21]=d;d=a[29]-d;a[29]=d;d=a[17]-a[1];a[17]=d;d=a[9]-d;a[9]=d;d=a[25]-d;a[25]=d;d=a[5]-d;a[5]=d;d=a[21]-d;a[21]=d;d=a[13]-d;a[13]=d;d=a[29]-d;a[29]=d;d=a[1]-a[0];a[1]=d;d=a[16]-d;a[16]=d;d=a[17]-d;a[17]=
d;d=a[8]-d;a[8]=d;d=a[9]-d;a[9]=d;d=a[24]-d;a[24]=d;d=a[25]-d;a[25]=d;d=a[4]-d;a[4]=d;d=a[5]-d;a[5]=d;d=a[20]-d;a[20]=d;d=a[21]-d;a[21]=d;d=a[12]-d;a[12]=d;d=a[13]-d;a[13]=d;d=a[28]-d;a[28]=d;d=a[29]-d;a[29]=d;d=a[0];a[0]+=a[31];a[31]-=d;d=a[1];a[1]+=a[30];a[30]-=d;d=a[16];a[16]+=a[15];a[15]-=d;d=a[17];a[17]+=a[14];a[14]-=d;d=a[8];a[8]+=a[23];a[23]-=d;d=a[9];a[9]+=a[22];a[22]-=d;d=a[24];a[24]+=a[7];a[7]-=d;d=a[25];a[25]+=a[6];a[6]-=d;d=a[4];a[4]+=a[27];a[27]-=d;d=a[5];a[5]+=a[26];a[26]-=d;d=a[20];
a[20]+=a[11];a[11]-=d;d=a[21];a[21]+=a[10];a[10]-=d;d=a[12];a[12]+=a[19];a[19]-=d;d=a[13];a[13]+=a[18];a[18]-=d;d=a[28];a[28]+=a[3];a[3]-=d;d=a[29];a[29]+=a[2];a[2]-=d}var p=[-0.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,-817.6293103748613,-76.91656988279972,-4.594269939176596,0.9063471690191471,0.1960342806591213,
-0.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,0.8206787908286602,0.3901806440322567,-0.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,
47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,0.7416505462720353,0.5805693545089249,-0.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,-1095.7960341867115,-118.24411666465777,-6.818469345853504,0.6681786379192989,0.7653668647301797,
-0.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,0.5993769336819237,0.9427934736519954,-0.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,
26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,0.5345111359507916,1.111140466039205,-0.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,-1373.62841526722,-166.1660487028118,-9.58289321133207,0.4729647758913199,1.268786568327291,
-0.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,0.41421356237309503,1.414213562373095,-0.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,
6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,0.3578057213145241,1.546020906725474,-0.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,0.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,-1625.2025997821418,-217.62525175416,-13.015432208941645,0.3033466836073424,1.66293922460509,
-0.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,0.2504869601913055,1.76384252869671,-0.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,
-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,0.198912367379658,1.847759065022573,-0.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,-1823.985884688674,-268.2854986386903,-16.81724543849939,0.1483359875383474,1.913880671464418,
-0.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,0.09849140335716425,1.961570560806461,-0.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,
-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,0.04912684976946725,1.990369453344394,0.0178904535*X.SQRT2/2.384E-6,0.008938074*X.SQRT2/2.384E-6,0.0015673635*X.SQRT2/2.384E-6,0.001228571*X.SQRT2/2.384E-6,4.856585E-4*X.SQRT2/2.384E-6,1.09434E-4*X.SQRT2/2.384E-6,5.0783E-5*X.SQRT2/2.384E-6,6.914E-6*X.SQRT2/2.384E-6,12804.797818791945,1945.5515939597317,313.4244966442953,20.801593959731544,1995.1556208053692,9.000838926174497,
-29.20218120805369],l=[[2.382191739347913E-13,6.423305872147834E-13,9.400849094049688E-13,1.122435026096556E-12,1.183840321267481E-12,1.122435026096556E-12,9.40084909404969E-13,6.423305872147839E-13,2.382191739347918E-13,5.456116108943412E-12,4.878985199565852E-12,4.240448995017367E-12,3.559909094758252E-12,2.858043359288075E-12,2.156177623817898E-12,1.475637723558783E-12,8.371015190102974E-13,2.599706096327376E-13,-5.456116108943412E-12,-4.878985199565852E-12,-4.240448995017367E-12,-3.559909094758252E-12,
-2.858043359288076E-12,-2.156177623817898E-12,-1.475637723558783E-12,-8.371015190102975E-13,-2.599706096327376E-13,-2.382191739347923E-13,-6.423305872147843E-13,-9.400849094049696E-13,-1.122435026096556E-12,-1.183840321267481E-12,-1.122435026096556E-12,-9.400849094049694E-13,-6.42330587214784E-13,-2.382191739347918E-13],[2.382191739347913E-13,6.423305872147834E-13,9.400849094049688E-13,1.122435026096556E-12,1.183840321267481E-12,1.122435026096556E-12,9.400849094049688E-13,6.423305872147841E-13,2.382191739347918E-13,
5.456116108943413E-12,4.878985199565852E-12,4.240448995017367E-12,3.559909094758253E-12,2.858043359288075E-12,2.156177623817898E-12,1.475637723558782E-12,8.371015190102975E-13,2.599706096327376E-13,-5.461314069809755E-12,-4.921085770524055E-12,-4.343405037091838E-12,-3.732668368707687E-12,-3.093523840190885E-12,-2.430835727329465E-12,-1.734679010007751E-12,-9.74825365660928E-13,-2.797435120168326E-13,0,0,0,0,0,0,-2.283748241799531E-13,-4.037858874020686E-13,-2.146547464825323E-13],[0.1316524975873958,
0.414213562373095,0.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,0.984807753012208,0.6427876096865394,0.3420201433256688,0.9396926207859084,-0.1736481776669303,-0.7660444431189779,0.8660254037844387,0.5,-0.5144957554275265,-0.4717319685649723,-0.3133774542039019,-0.1819131996109812,-0.09457419252642064,-0.04096558288530405,-0.01419856857247115,-0.003699974673760037,
0.8574929257125442,0.8817419973177052,0.9496286491027329,0.9833145924917901,0.9955178160675857,0.9991605581781475,0.999899195244447,0.9999931550702802],[0,0,0,0,0,0,2.283748241799531E-13,4.037858874020686E-13,2.146547464825323E-13,5.461314069809755E-12,4.921085770524055E-12,4.343405037091838E-12,3.732668368707687E-12,3.093523840190885E-12,2.430835727329466E-12,1.734679010007751E-12,9.74825365660928E-13,2.797435120168326E-13,-5.456116108943413E-12,-4.878985199565852E-12,-4.240448995017367E-12,-3.559909094758253E-12,
-2.858043359288075E-12,-2.156177623817898E-12,-1.475637723558782E-12,-8.371015190102975E-13,-2.599706096327376E-13,-2.382191739347913E-13,-6.423305872147834E-13,-9.400849094049688E-13,-1.122435026096556E-12,-1.183840321267481E-12,-1.122435026096556E-12,-9.400849094049688E-13,-6.423305872147841E-13,-2.382191739347918E-13]],z=l[e.SHORT_TYPE],x=l[e.SHORT_TYPE],u=l[e.SHORT_TYPE],q=l[e.SHORT_TYPE],h=[0,1,16,17,8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];this.mdct_sub48=
function(b,n,a){for(var f=286,p=0;p<b.channels_out;p++){for(var k=0;k<b.mode_gr;k++){for(var g,d=b.l3_side.tt[k][p],c=d.xr,B=0,y=b.sb_sample[p][1-k],r=0,s=0;9>s;s++){v(n,f,y[r]);v(n,f+32,y[r+1]);r+=2;f+=64;for(g=1;32>g;g+=2)y[r-1][g]*=-1}for(g=0;32>g;g++,B+=18){var y=d.block_type,r=b.sb_sample[p][k],j=b.sb_sample[p][1-k];0!=d.mixed_block_flag&&2>g&&(y=0);if(1E-12>b.amp_filter[g])Ba.fill(c,B+0,B+18,0);else{if(1>b.amp_filter[g])for(s=0;18>s;s++)j[s][h[g]]*=b.amp_filter[g];if(y==e.SHORT_TYPE){for(s=
-3;0>s;s++){var i=l[e.SHORT_TYPE][s+3];c[B+3*s+9]=r[9+s][h[g]]*i-r[8-s][h[g]];c[B+3*s+18]=r[14-s][h[g]]*i+r[15+s][h[g]];c[B+3*s+10]=r[15+s][h[g]]*i-r[14-s][h[g]];c[B+3*s+19]=j[2-s][h[g]]*i+j[3+s][h[g]];c[B+3*s+11]=j[3+s][h[g]]*i-j[2-s][h[g]];c[B+3*s+20]=j[8-s][h[g]]*i+j[9+s][h[g]]}s=c;r=B;for(i=0;3>i;i++){var m,w,t,A,G;t=s[r+6]*l[e.SHORT_TYPE][0]-s[r+15];j=s[r+0]*l[e.SHORT_TYPE][2]-s[r+9];m=t+j;w=t-j;t=s[r+15]*l[e.SHORT_TYPE][0]+s[r+6];j=s[r+9]*l[e.SHORT_TYPE][2]+s[r+0];A=t+j;G=-t+j;j=2.069978111953089E-11*
(s[r+3]*l[e.SHORT_TYPE][1]-s[r+12]);t=2.069978111953089E-11*(s[r+12]*l[e.SHORT_TYPE][1]+s[r+3]);s[r+0]=1.90752519173728E-11*m+j;s[r+15]=1.90752519173728E-11*-A+t;w*=1.6519652744032674E-11;A=9.537625958686404E-12*A+t;s[r+3]=w-A;s[r+6]=w+A;m=9.537625958686404E-12*m-j;G*=1.6519652744032674E-11;s[r+9]=m+G;s[r+12]=m-G;r++}}else{i=K(18);for(s=-9;0>s;s++)m=l[y][s+27]*j[s+9][h[g]]+l[y][s+36]*j[8-s][h[g]],w=l[y][s+9]*r[s+9][h[g]]-l[y][s+18]*r[8-s][h[g]],i[s+9]=m-w*z[3+s+9],i[s+18]=m*z[3+s+9]+w;var s=c,r=B,
F=G=A=t=w=m=j=void 0,E=void 0,C=void 0,L=void 0;w=i[17]-i[9];A=i[15]-i[11];G=i[14]-i[12];F=i[0]+i[8];E=i[1]+i[7];C=i[2]+i[6];L=i[3]+i[5];s[r+17]=F+C-L-(E-i[4]);m=(F+C-L)*x[19]+(E-i[4]);j=(w-A-G)*x[18];s[r+5]=j+m;s[r+6]=j-m;t=(i[16]-i[10])*x[18];E=E*x[19]+i[4];j=w*x[12]+t+A*x[13]+G*x[14];m=-F*x[16]+E-C*x[17]+L*x[15];s[r+1]=j+m;s[r+2]=j-m;j=w*x[13]-t-A*x[14]+G*x[12];m=-F*x[17]+E-C*x[15]+L*x[16];s[r+9]=j+m;s[r+10]=j-m;j=w*x[14]-t+A*x[12]-G*x[13];m=F*x[15]-E+C*x[16]-L*x[17];s[r+13]=j+m;s[r+14]=j-m;L=
C=E=F=G=A=t=w=void 0;w=i[8]-i[0];A=i[6]-i[2];G=i[5]-i[3];F=i[17]+i[9];E=i[16]+i[10];C=i[15]+i[11];L=i[14]+i[12];s[r+0]=F+C+L+(E+i[13]);j=(F+C+L)*x[19]-(E+i[13]);m=(w-A+G)*x[18];s[r+11]=j+m;s[r+12]=j-m;t=(i[7]-i[1])*x[18];E=i[13]-E*x[19];j=F*x[15]-E+C*x[16]+L*x[17];m=w*x[14]+t+A*x[12]+G*x[13];s[r+3]=j+m;s[r+4]=j-m;j=-F*x[17]+E-C*x[15]-L*x[16];m=w*x[13]+t-A*x[14]-G*x[12];s[r+7]=j+m;s[r+8]=j-m;j=-F*x[16]+E-C*x[17]-L*x[15];m=w*x[12]-t+A*x[13]-G*x[14];s[r+15]=j+m;s[r+16]=j-m}}if(y!=e.SHORT_TYPE&&0!=g)for(s=
7;0<=s;--s)y=c[B+s]*u[20+s]+c[B+-1-s]*q[28+s],r=c[B+s]*q[28+s]-c[B+-1-s]*u[20+s],c[B+-1-s]=y,c[B+s]=r}}n=a;f=286;if(1==b.mode_gr)for(k=0;18>k;k++)N.arraycopy(b.sb_sample[p][1][k],0,b.sb_sample[p][0][k],0,32)}}}function za(){this.thm=new Za;this.en=new Za}function e(){var v=e.FFTOFFSET,p=e.MPG_MD_MS_LR,l=null,z=this.psy=null,x=null,u=null;this.setModules=function(e,b,n,a){l=e;z=this.psy=b;x=a;u=n};var q=new Kc;this.lame_encode_mp3_frame=function(h,b,n,a,f,J){var k=ac([2,2]);k[0][0]=new za;k[0][1]=
new za;k[1][0]=new za;k[1][1]=new za;var g=ac([2,2]);g[0][0]=new za;g[0][1]=new za;g[1][0]=new za;g[1][1]=new za;var d=[null,null],c=h.internal_flags,B=qa([2,4]),y=[0.5,0.5],r=[[0,0],[0,0]],s=[[0,0],[0,0]];d[0]=b;d[1]=n;if(0==c.lame_encode_frame_init){var b=h.internal_flags,j,i;if(0==b.lame_encode_frame_init){var n=K(2014),m=K(2014);b.lame_encode_frame_init=1;for(i=j=0;j<286+576*(1+b.mode_gr);++j)j<576*b.mode_gr?(n[j]=0,2==b.channels_out&&(m[j]=0)):(n[j]=d[0][i],2==b.channels_out&&(m[j]=d[1][i]),
++i);for(i=0;i<b.mode_gr;i++)for(j=0;j<b.channels_out;j++)b.l3_side.tt[i][j].block_type=e.SHORT_TYPE;q.mdct_sub48(b,n,m)}}c.padding=0;if(0>(c.slot_lag-=c.frac_SpF))c.slot_lag+=h.out_samplerate,c.padding=1;if(0!=c.psymodel){m=[null,null];j=0;i=T(2);for(n=0;n<c.mode_gr;n++){for(b=0;b<c.channels_out;b++)m[b]=d[b],j=576+576*n-e.FFTOFFSET;b=h.VBR==F.vbr_mtrh||h.VBR==F.vbr_mt?z.L3psycho_anal_vbr(h,m,j,n,k,g,r[n],s[n],B[n],i):z.L3psycho_anal_ns(h,m,j,n,k,g,r[n],s[n],B[n],i);if(0!=b)return-4;h.mode==ia.JOINT_STEREO&&
(y[n]=B[n][2]+B[n][3],0<y[n]&&(y[n]=B[n][3]/y[n]));for(b=0;b<c.channels_out;b++){var w=c.l3_side.tt[n][b];w.block_type=i[b];w.mixed_block_flag=0}}}else for(n=0;n<c.mode_gr;n++)for(b=0;b<c.channels_out;b++)c.l3_side.tt[n][b].block_type=e.NORM_TYPE,c.l3_side.tt[n][b].mixed_block_flag=0,s[n][b]=r[n][b]=700;0==c.ATH.useAdjust?c.ATH.adjust=1:(b=c.loudness_sq[0][0],B=c.loudness_sq[1][0],2==c.channels_out?(b+=c.loudness_sq[0][1],B+=c.loudness_sq[1][1]):(b+=b,B+=B),2==c.mode_gr&&(b=Math.max(b,B)),b=0.5*b*
c.ATH.aaSensitivityP,0.03125<b?(1<=c.ATH.adjust?c.ATH.adjust=1:c.ATH.adjust<c.ATH.adjustLimit&&(c.ATH.adjust=c.ATH.adjustLimit),c.ATH.adjustLimit=1):(B=31.98*b+6.25E-4,c.ATH.adjust>=B?(c.ATH.adjust*=0.075*B+0.925,c.ATH.adjust<B&&(c.ATH.adjust=B)):c.ATH.adjustLimit>=B?c.ATH.adjust=B:c.ATH.adjust<c.ATH.adjustLimit&&(c.ATH.adjust=c.ATH.adjustLimit),c.ATH.adjustLimit=B));q.mdct_sub48(c,d[0],d[1]);c.mode_ext=e.MPG_MD_LR_LR;if(h.force_ms)c.mode_ext=e.MPG_MD_MS_LR;else if(h.mode==ia.JOINT_STEREO){for(n=
m=B=0;n<c.mode_gr;n++)for(b=0;b<c.channels_out;b++)B+=s[n][b],m+=r[n][b];B<=1*m&&(B=c.l3_side.tt[0],b=c.l3_side.tt[c.mode_gr-1],B[0].block_type==B[1].block_type&&b[0].block_type==b[1].block_type&&(c.mode_ext=e.MPG_MD_MS_LR))}c.mode_ext==p&&(k=g,r=s);if(h.analysis&&null!=c.pinfo)for(n=0;n<c.mode_gr;n++)for(b=0;b<c.channels_out;b++)c.pinfo.ms_ratio[n]=c.ms_ratio[n],c.pinfo.ms_ener_ratio[n]=y[n],c.pinfo.blocktype[n][b]=c.l3_side.tt[n][b].block_type,c.pinfo.pe[n][b]=r[n][b],N.arraycopy(c.l3_side.tt[n][b].xr,
0,c.pinfo.xr[n][b],0,576),c.mode_ext==p&&(c.pinfo.ers[n][b]=c.pinfo.ers[n][b+2],N.arraycopy(c.pinfo.energy[n][b+2],0,c.pinfo.energy[n][b],0,c.pinfo.energy[n][b].length));if(h.VBR==F.vbr_off||h.VBR==F.vbr_abr){for(g=0;18>g;g++)c.nsPsy.pefirbuf[g]=c.nsPsy.pefirbuf[g+1];for(n=s=0;n<c.mode_gr;n++)for(b=0;b<c.channels_out;b++)s+=r[n][b];c.nsPsy.pefirbuf[18]=s;s=c.nsPsy.pefirbuf[9];for(g=0;9>g;g++)s+=(c.nsPsy.pefirbuf[g]+c.nsPsy.pefirbuf[18-g])*e.fircoef[g];s=3350*c.mode_gr*c.channels_out/s;for(n=0;n<c.mode_gr;n++)for(b=
0;b<c.channels_out;b++)r[n][b]*=s}c.iteration_loop.iteration_loop(h,r,y,k);l.format_bitstream(h);a=l.copy_buffer(c,a,f,J,1);h.bWriteVbrTag&&x.addVbrFrame(h);if(h.analysis&&null!=c.pinfo){for(b=0;b<c.channels_out;b++){for(f=0;f<v;f++)c.pinfo.pcmdata[b][f]=c.pinfo.pcmdata[b][f+h.framesize];for(f=v;1600>f;f++)c.pinfo.pcmdata[b][f]=d[b][f-v]}u.set_frame_pinfo(h,k)}c.bitrate_stereoMode_Hist[c.bitrate_index][4]++;c.bitrate_stereoMode_Hist[15][4]++;2==c.channels_out&&(c.bitrate_stereoMode_Hist[c.bitrate_index][c.mode_ext]++,
c.bitrate_stereoMode_Hist[15][c.mode_ext]++);for(h=0;h<c.mode_gr;++h)for(d=0;d<c.channels_out;++d)f=c.l3_side.tt[h][d].block_type|0,0!=c.l3_side.tt[h][d].mixed_block_flag&&(f=4),c.bitrate_blockType_Hist[c.bitrate_index][f]++,c.bitrate_blockType_Hist[c.bitrate_index][5]++,c.bitrate_blockType_Hist[15][f]++,c.bitrate_blockType_Hist[15][5]++;return a}}function Lc(){this.size=this.pos=this.want=this.seen=this.sum=0;this.bag=null;this.TotalFrameSize=this.nBytesWritten=this.nVbrNumFrames=0}function Mc(){this.tt=
[[null,null],[null,null]];this.resvDrain_post=this.resvDrain_pre=this.private_bits=this.main_data_begin=0;this.scfsi=[T(4),T(4)];for(var e=0;2>e;e++)for(var p=0;2>p;p++)this.tt[e][p]=new gb}function Nc(){this.last_en_subshort=qa([4,9]);this.lastAttacks=T(4);this.pefirbuf=K(19);this.longfact=K(e.SBMAX_l);this.shortfact=K(e.SBMAX_s);this.attackthre_s=this.attackthre=0}function Za(){this.l=K(e.SBMAX_l);this.s=qa([e.SBMAX_s,3]);var v=this;this.assign=function(p){N.arraycopy(p.l,0,v.l,0,e.SBMAX_l);for(var l=
0;l<e.SBMAX_s;l++)for(var z=0;3>z;z++)v.s[l][z]=p.s[l][z]}}function aa(){function v(){this.ptr=this.write_timing=0;this.buf=new Int8Array(p)}var p=40;this.fill_buffer_resample_init=this.iteration_init_init=this.lame_encode_frame_init=this.Class_ID=0;this.mfbuf=qa([2,aa.MFSIZE]);this.full_outer_loop=this.use_best_huffman=this.subblock_gain=this.noise_shaping_stop=this.psymodel=this.substep_shaping=this.noise_shaping_amp=this.noise_shaping=this.highpass2=this.highpass1=this.lowpass2=this.lowpass1=this.mode_ext=
this.samplerate_index=this.bitrate_index=this.VBR_max_bitrate=this.VBR_min_bitrate=this.mf_size=this.mf_samples_to_encode=this.resample_ratio=this.channels_out=this.channels_in=this.mode_gr=0;this.l3_side=new Mc;this.ms_ratio=K(2);this.slot_lag=this.frac_SpF=this.padding=0;this.tag_spec=null;this.nMusicCRC=0;this.OldValue=T(2);this.CurrentStep=T(2);this.masking_lower=0;this.bv_scf=T(576);this.pseudohalf=T(ra.SFBMAX);this.sfb21_extra=!1;this.inbuf_old=Array(2);this.blackfilt=Array(2*aa.BPC+1);this.itime=
new Float64Array(2);this.sideinfo_len=0;this.sb_sample=qa([2,2,18,e.SBLIMIT]);this.amp_filter=K(32);this.header=Array(aa.MAX_HEADER_BUF);this.ResvMax=this.ResvSize=this.ancillary_flag=this.w_ptr=this.h_ptr=0;this.scalefac_band=new la;this.minval_l=K(e.CBANDS);this.minval_s=K(e.CBANDS);this.nb_1=qa([4,e.CBANDS]);this.nb_2=qa([4,e.CBANDS]);this.nb_s1=qa([4,e.CBANDS]);this.nb_s2=qa([4,e.CBANDS]);this.s3_ll=this.s3_ss=null;this.decay=0;this.thm=Array(4);this.en=Array(4);this.tot_ener=K(4);this.loudness_sq=
qa([2,2]);this.loudness_sq_save=K(2);this.mld_l=K(e.SBMAX_l);this.mld_s=K(e.SBMAX_s);this.bm_l=T(e.SBMAX_l);this.bo_l=T(e.SBMAX_l);this.bm_s=T(e.SBMAX_s);this.bo_s=T(e.SBMAX_s);this.npart_s=this.npart_l=0;this.s3ind=va([e.CBANDS,2]);this.s3ind_s=va([e.CBANDS,2]);this.numlines_s=T(e.CBANDS);this.numlines_l=T(e.CBANDS);this.rnumlines_l=K(e.CBANDS);this.mld_cb_l=K(e.CBANDS);this.mld_cb_s=K(e.CBANDS);this.numlines_l_num1=this.numlines_s_num1=0;this.pe=K(4);this.ms_ener_ratio_old=this.ms_ratio_l_old=this.ms_ratio_s_old=
0;this.blocktype_old=T(2);this.nsPsy=new Nc;this.VBR_seek_table=new Lc;this.PSY=this.ATH=null;this.nogap_current=this.nogap_total=0;this.findPeakSample=this.findReplayGain=this.decode_on_the_fly=!0;this.AudiophileGain=this.RadioGain=this.PeakSample=0;this.rgdata=null;this.noclipScale=this.noclipGainChange=0;this.bitrate_stereoMode_Hist=va([16,5]);this.bitrate_blockType_Hist=va([16,6]);this.hip=this.pinfo=null;this.in_buffer_nsamples=0;this.iteration_loop=this.in_buffer_1=this.in_buffer_0=null;for(var l=
0;l<this.en.length;l++)this.en[l]=new Za;for(l=0;l<this.thm.length;l++)this.thm[l]=new Za;for(l=0;l<this.header.length;l++)this.header[l]=new v}function Oc(){function v(e,l,h){var b=0,n,a,f,h=h<<1,p=l+h;n=4;do{var k,g,d,c,x,y,r;r=n>>1;c=n;x=n<<1;y=x+c;n=x<<1;a=l;f=a+r;do{var s,j,i,m;j=e[a+0]-e[a+c];s=e[a+0]+e[a+c];m=e[a+x]-e[a+y];i=e[a+x]+e[a+y];e[a+x]=s-i;e[a+0]=s+i;e[a+y]=j-m;e[a+c]=j+m;j=e[f+0]-e[f+c];s=e[f+0]+e[f+c];m=X.SQRT2*e[f+y];i=X.SQRT2*e[f+x];e[f+x]=s-i;e[f+0]=s+i;e[f+y]=j-m;e[f+c]=j+m;
f+=n;a+=n}while(a<p);g=z[b+0];k=z[b+1];for(d=1;d<r;d++){var w,t;w=1-2*k*k;t=2*k*g;a=l+d;f=l+c-d;do{var A,G,D,E,C;A=t*e[a+c]-w*e[f+c];i=w*e[a+c]+t*e[f+c];j=e[a+0]-i;s=e[a+0]+i;D=e[f+0]-A;G=e[f+0]+A;A=t*e[a+y]-w*e[f+y];i=w*e[a+y]+t*e[f+y];m=e[a+x]-i;i=e[a+x]+i;C=e[f+x]-A;E=e[f+x]+A;A=k*i-g*C;i=g*i+k*C;e[a+x]=s-i;e[a+0]=s+i;e[f+y]=D-A;e[f+c]=D+A;A=g*E-k*m;i=k*E+g*m;e[f+x]=G-i;e[f+0]=G+i;e[a+y]=j-A;e[a+c]=j+A;f+=n;a+=n}while(a<p);w=g;g=w*z[b+0]-k*z[b+1];k=w*z[b+1]+k*z[b+0]}b+=2}while(n<h)}var p=K(e.BLKSIZE),
l=K(e.BLKSIZE_s/2),z=[0.9238795325112867,0.3826834323650898,0.9951847266721969,0.0980171403295606,0.9996988186962042,0.02454122852291229,0.9999811752826011,0.006135884649154475],x=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,
90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(p,q,h,b,n){for(p=0;3>p;p++){var a=e.BLKSIZE_s/2,f=65535&192*(p+1),z=e.BLKSIZE_s/8-1;do{var k,g,d,c,B,y=x[z<<2]&255;k=l[y]*b[h][n+y+f];B=l[127-y]*b[h][n+y+f+128];g=k-B;k+=B;d=l[y+64]*b[h][n+y+f+64];B=l[63-y]*b[h][n+y+f+192];c=d-B;d+=B;a-=4;q[p][a+0]=k+d;q[p][a+2]=k-d;q[p][a+1]=g+c;q[p][a+3]=g-c;k=l[y+1]*b[h][n+y+f+1];B=l[126-y]*b[h][n+y+f+
129];g=k-B;k+=B;d=l[y+65]*b[h][n+y+f+65];B=l[62-y]*b[h][n+y+f+193];c=d-B;d+=B;q[p][a+e.BLKSIZE_s/2+0]=k+d;q[p][a+e.BLKSIZE_s/2+2]=k-d;q[p][a+e.BLKSIZE_s/2+1]=g+c;q[p][a+e.BLKSIZE_s/2+3]=g-c}while(0<=--z);v(q[p],a,e.BLKSIZE_s/2)}};this.fft_long=function(l,q,h,b,n){var l=e.BLKSIZE/8-1,a=e.BLKSIZE/2;do{var f,z,k,g,d,c=x[l]&255;f=p[c]*b[h][n+c];d=p[c+512]*b[h][n+c+512];z=f-d;f+=d;k=p[c+256]*b[h][n+c+256];d=p[c+768]*b[h][n+c+768];g=k-d;k+=d;a-=4;q[a+0]=f+k;q[a+2]=f-k;q[a+1]=z+g;q[a+3]=z-g;f=p[c+1]*b[h][n+
c+1];d=p[c+513]*b[h][n+c+513];z=f-d;f+=d;k=p[c+257]*b[h][n+c+257];d=p[c+769]*b[h][n+c+769];g=k-d;k+=d;q[a+e.BLKSIZE/2+0]=f+k;q[a+e.BLKSIZE/2+2]=f-k;q[a+e.BLKSIZE/2+1]=z+g;q[a+e.BLKSIZE/2+3]=z-g}while(0<=--l);v(q,a,e.BLKSIZE/2)};this.init_fft=function(){for(var u=0;u<e.BLKSIZE;u++)p[u]=0.42-0.5*Math.cos(2*Math.PI*(u+0.5)/e.BLKSIZE)+0.08*Math.cos(4*Math.PI*(u+0.5)/e.BLKSIZE);for(u=0;u<e.BLKSIZE_s/2;u++)l[u]=0.5*(1-Math.cos(2*Math.PI*(u+0.5)/e.BLKSIZE_s))}}function Sb(){function v(a,b){for(var d=0,m=
0;m<e.BLKSIZE/2;++m)d+=a[m]*b.ATH.eql_w[m];return d*=s}function p(a,b,e,d,c,f){var g;if(b>a)if(b<a*m)g=b/a;else return a+b;else{if(a>=b*m)return a+b;g=a/b}a+=b;if(6>=d+3){if(g>=i)return a;d=0|X.FAST_LOG10_X(g,16);return a*G[d]}d=0|X.FAST_LOG10_X(g,16);b=0!=f?c.ATH.cb_s[e]*c.ATH.adjust:c.ATH.cb_l[e]*c.ATH.adjust;return a<w*b?a>b?(e=1,13>=d&&(e=N[d]),b=X.FAST_LOG10_X(a/b,10/15),a*((A[d]-e)*b+e)):13<d?a:a*N[d]:a*A[d]}function l(a,b,e){var d;0>a&&(a=0);0>b&&(b=0);if(0>=a)return b;if(0>=b)return a;d=b>
a?b/a:a/b;if(-2<=e&&2>=e){if(d>=i)return a+b;e=0|X.FAST_LOG10_X(d,16);return(a+b)*Ca[e]}if(d<m)return a+b;a<b&&(a=b);return a}function z(a,b,d,m,c){var f,g,i=0,h=0;for(f=g=0;f<e.SBMAX_s;++g,++f){for(var k=a.bo_s[f],t=a.npart_s,k=k<t?k:t;g<k;)i+=b[g],h+=d[g],g++;a.en[m].s[f][c]=i;a.thm[m].s[f][c]=h;if(g>=t){++f;break}h=a.PSY.bo_s_weight[f];t=1-h;i=h*b[g];h*=d[g];a.en[m].s[f][c]+=i;a.thm[m].s[f][c]+=h;i=t*b[g];h=t*d[g]}for(;f<e.SBMAX_s;++f)a.en[m].s[f][c]=0,a.thm[m].s[f][c]=0}function x(a,b,d,m){var c,
f,g=0,i=0;for(c=f=0;c<e.SBMAX_l;++f,++c){for(var h=a.bo_l[c],k=a.npart_l,h=h<k?h:k;f<h;)g+=b[f],i+=d[f],f++;a.en[m].l[c]=g;a.thm[m].l[c]=i;if(f>=k){++c;break}i=a.PSY.bo_l_weight[c];k=1-i;g=i*b[f];i*=d[f];a.en[m].l[c]+=g;a.thm[m].l[c]+=i;g=k*b[f];i=k*d[f]}for(;c<e.SBMAX_l;++c)a.en[m].l[c]=0,a.thm[m].l[c]=0}function u(a,b,e){return 1<=e?a:0>=e?b:0<b?Math.pow(a/b,e)*b:0}function q(a,b){for(var d=309.07,m=0;m<e.SBMAX_s-1;m++)for(var c=0;3>c;c++){var f=a.thm.s[m][c];if(0<f){var f=f*b,g=a.en.s[m][c];g>
f&&(d=g>1E10*f?d+C[m]*10*y:d+C[m]*X.FAST_LOG10(g/f))}}return d}function h(a,b){for(var d=281.0575,m=0;m<e.SBMAX_l-1;m++){var c=a.thm.l[m];if(0<c){var c=c*b,f=a.en.l[m];f>c&&(d=f>1E10*c?d+L[m]*10*y:d+L[m]*X.FAST_LOG10(f/c))}}return d}function b(a,b,e,d,m){var c,f;for(c=f=0;c<a.npart_l;++c){var g=0,i=0,h;for(h=0;h<a.numlines_l[c];++h,++f){var k=b[f],g=g+k;i<k&&(i=k)}e[c]=g;d[c]=i;m[c]=g*a.rnumlines_l[c]}}function n(a,b,e,d){var m=t.length-1,c=0,f=e[c]+e[c+1];if(0<f){var g=b[c];g<b[c+1]&&(g=b[c+1]);
f=20*(2*g-f)/(f*(a.numlines_l[c]+a.numlines_l[c+1]-1));f|=0;f>m&&(f=m);d[c]=f}else d[c]=0;for(c=1;c<a.npart_l-1;c++)f=e[c-1]+e[c]+e[c+1],0<f?(g=b[c-1],g<b[c]&&(g=b[c]),g<b[c+1]&&(g=b[c+1]),f=20*(3*g-f)/(f*(a.numlines_l[c-1]+a.numlines_l[c]+a.numlines_l[c+1]-1)),f|=0,f>m&&(f=m),d[c]=f):d[c]=0;f=e[c-1]+e[c];0<f?(g=b[c-1],g<b[c]&&(g=b[c]),f=20*(2*g-f)/(f*(a.numlines_l[c-1]+a.numlines_l[c]-1)),f|=0,f>m&&(f=m),d[c]=f):d[c]=0}function a(a,b,e,d,m,c,f){for(var g=2*c,m=0<c?Math.pow(10,m):1,i,h,k=0;k<f;++k){var t=
a[2][k],l=a[3][k],w=b[0][k],j=b[1][k],n=b[2][k],p=b[3][k];w<=1.58*j&&j<=1.58*w?(i=e[k]*t,h=Math.max(n,Math.min(p,e[k]*l)),i=Math.max(p,Math.min(n,i))):(h=n,i=p);0<c&&(p=d[k]*m,w=Math.min(Math.max(w,p),Math.max(j,p)),n=Math.max(h,p),p=Math.max(i,p),j=n+p,0<j&&w*g<j&&(w=w*g/j,n*=w,p*=w),h=Math.min(n,h),i=Math.min(p,i));h>t&&(h=t);i>l&&(i=l);b[2][k]=h;b[3][k]=i}}function f(a,b){var e;e=0<=a?27*-a:a*b;return-72>=e?0:Math.exp(e*j)}function E(a){0>a&&(a=0);a*=0.001;return 13*Math.atan(0.76*a)+3.5*Math.atan(a*
a/56.25)}function k(a,b,d,m,c,f,g,i,h,k,t,l){var w=K(e.CBANDS+1),j=i/(15<l?1152:384),n=T(e.HBLKSIZE),p,i=i/h,q=0,u=0;for(p=0;p<e.CBANDS;p++){var A,s;A=E(i*q);w[p]=i*q;for(s=q;E(i*s)-A<r&&s<=h/2;s++);a[p]=s-q;for(u=p+1;q<s;)n[q++]=p;if(q>h/2){q=h/2;++p;break}}w[p]=i*q;for(q=0;q<l;q++)p=k[q],A=k[q+1],p=0|Math.floor(0.5+t*(p-0.5)),0>p&&(p=0),s=0|Math.floor(0.5+t*(A-0.5)),s>h/2&&(s=h/2),d[q]=(n[p]+n[s])/2,b[q]=n[s],g[q]=(j*A-w[b[q]])/(w[b[q]+1]-w[b[q]]),0>g[q]?g[q]=0:1<g[q]&&(g[q]=1),A=E(i*k[q]*t),A=
Math.min(A,15.5)/15.5,f[q]=Math.pow(10,1.25*(1-Math.cos(Math.PI*A))-2.5);for(b=q=0;b<u;b++)d=a[b],A=E(i*q),f=E(i*(q+d-1)),m[b]=0.5*(A+f),A=E(i*(q-0.5)),f=E(i*(q+d-0.5)),c[b]=f-A,q+=d;return u}function g(a,b,d,m,c,g){var i=qa([e.CBANDS,e.CBANDS]),h=0;if(g)for(var k=0;k<b;k++)for(g=0;g<b;g++){var t;var l=t=void 0,l=t=void 0;t=d[k]-d[g];t=0<=t?3*t:1.5*t;0.5<=t&&2.5>=t?(l=t-0.5,l=8*(l*l-2*l)):l=0;t+=0.474;t=15.811389+7.5*t-17.5*Math.sqrt(1+t*t);-60>=t?t=0:(t=Math.exp((l+t)*j),t/=0.6609193);l=t*m[g];i[k][g]=
l*c[k]}else for(g=0;g<b;g++){t=15+Math.min(21/d[g],12);var w;var k=t,p=l=0;w=0;var n=p=void 0;for(w=0;1E-20<f(w,k);w-=1);p=w;for(n=0;1E-12<Math.abs(n-p);)w=(n+p)/2,0<f(w,k)?n=w:p=w;l=p;for(w=0;1E-20<f(w,k);w+=1);p=0;for(n=w;1E-12<Math.abs(n-p);)w=(n+p)/2,0<f(w,k)?p=w:n=w;for(var p=n,n=0,q=void 0,q=0;1E3>=q;++q)w=l+q*(p-l)/1E3,w=f(w,k),n+=w;w=1001/(n*(p-l));for(k=0;k<b;k++)l=w*f(d[k]-d[g],t)*m[g],i[k][g]=l*c[k]}for(k=0;k<b;k++){for(g=0;g<b&&!(0<i[k][g]);g++);a[k][0]=g;for(g=b-1;0<g&&!(0<i[k][g]);g--);
a[k][1]=g;h+=a[k][1]-a[k][0]+1}d=K(h);for(k=m=0;k<b;k++)for(g=a[k][0];g<=a[k][1];g++)d[m++]=i[k][g];return d}function d(a){a=E(a);a=Math.min(a,15.5)/15.5;return Math.pow(10,1.25*(1-Math.cos(Math.PI*a))-2.5)}function c(a,b){-0.3>a&&(a=3410);a=Math.max(0.1,a/1E3);return 3.64*Math.pow(a,-0.8)-6.8*Math.exp(-0.6*Math.pow(a-3.4,2))+6*Math.exp(-0.15*Math.pow(a-8.7,2))+0.001*(0.6+0.04*b)*Math.pow(a,4)}var B=new Oc,y=2.302585092994046,r=0.34,s=1/217621504/(e.BLKSIZE/2),j=0.2302585093,i,m,w,t=[1,0.79433,0.63096,
0.63096,0.63096,0.63096,0.63096,0.25119,0.11749],A=[3.3246*3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],G=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,
1.22321*1.22321,1.3169398564,1],N=[5.5396212496,2.29259*2.29259,4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276],Ca=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],C=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130],L=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,
34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1],R=[-1.730326E-17,-0.01703172,-1.349528E-17,0.0418072,-6.73278E-17,-0.0876324,-3.0835E-17,0.1863476,-1.104424E-16,-0.627638];this.L3psycho_anal_ns=function(a,d,m,c,f,g,i,k,w,l){var j=a.internal_flags,r=qa([2,e.BLKSIZE]),A=qa([2,3,e.BLKSIZE_s]),s=K(e.CBANDS+1),C=K(e.CBANDS+1),y=K(e.CBANDS+2),G=T(2),E=T(2),L,I,S,H,J,Ca,N,Z,U=qa([2,576]),Q,W=T(e.CBANDS+2),M=T(e.CBANDS+2);Ba.fill(M,0);L=j.channels_out;a.mode==ia.JOINT_STEREO&&(L=4);Q=a.VBR==F.vbr_off?0==j.ResvMax?
0:0.5*(j.ResvSize/j.ResvMax):a.VBR==F.vbr_rh||a.VBR==F.vbr_mtrh||a.VBR==F.vbr_mt?0.6:1;for(I=0;I<j.channels_out;I++){var aa=d[I],tb=m+576-350-21+192;for(H=0;576>H;H++){var oc,ka;oc=aa[tb+H+10];for(J=ka=0;9>J;J+=2)oc+=R[J]*(aa[tb+H+J]+aa[tb+H+21-J]),ka+=R[J+1]*(aa[tb+H+J+1]+aa[tb+H+21-J-1]);U[I][H]=oc+ka}f[c][I].en.assign(j.en[I]);f[c][I].thm.assign(j.thm[I]);2<L&&(g[c][I].en.assign(j.en[I+2]),g[c][I].thm.assign(j.thm[I+2]))}for(I=0;I<L;I++){var ma=K(12),Pa=[0,0,0,0],ca=K(12),ra=1,xa,la=K(e.CBANDS),
za=K(e.CBANDS),na=[0,0,0,0],va=K(e.HBLKSIZE),$a=qa([3,e.HBLKSIZE_s]);for(H=0;3>H;H++)ma[H]=j.nsPsy.last_en_subshort[I][H+6],ca[H]=ma[H]/j.nsPsy.last_en_subshort[I][H+4],Pa[0]+=ma[H];if(2==I)for(H=0;576>H;H++){var Ha,zb;Ha=U[0][H];zb=U[1][H];U[0][H]=Ha+zb;U[1][H]=Ha-zb}var Ma=U[I&1],Ab=0;for(H=0;9>H;H++){for(var La=Ab+64,Na=1;Ab<La;Ab++)Na<Math.abs(Ma[Ab])&&(Na=Math.abs(Ma[Ab]));j.nsPsy.last_en_subshort[I][H]=ma[H+3]=Na;Pa[1+H/3]+=Na;Na=Na>ma[H+3-2]?Na/ma[H+3-2]:ma[H+3-2]>10*Na?ma[H+3-2]/(10*Na):0;
ca[H+3]=Na}if(a.analysis){var Hb=ca[0];for(H=1;12>H;H++)Hb<ca[H]&&(Hb=ca[H]);j.pinfo.ers[c][I]=j.pinfo.ers_save[I];j.pinfo.ers_save[I]=Hb}xa=3==I?j.nsPsy.attackthre_s:j.nsPsy.attackthre;for(H=0;12>H;H++)0==na[H/3]&&ca[H]>xa&&(na[H/3]=H%3+1);for(H=1;4>H;H++)if(1.7>(Pa[H-1]>Pa[H]?Pa[H-1]/Pa[H]:Pa[H]/Pa[H-1]))na[H]=0,1==H&&(na[0]=0);0!=na[0]&&0!=j.nsPsy.lastAttacks[I]&&(na[0]=0);if(3==j.nsPsy.lastAttacks[I]||0!=na[0]+na[1]+na[2]+na[3])ra=0,0!=na[1]&&0!=na[0]&&(na[1]=0),0!=na[2]&&0!=na[1]&&(na[2]=0),
0!=na[3]&&0!=na[2]&&(na[3]=0);2>I?E[I]=ra:0==ra&&(E[0]=E[1]=0);w[I]=j.tot_ener[I];var P=a,Ja=va,Ib=$a,Oa=r,pb=I&1,Ta=A,Qa=I&1,hb=c,Ea=I,sa=d,Da=m,Va=P.internal_flags;if(2>Ea)B.fft_long(Va,Oa[pb],Ea,sa,Da),B.fft_short(Va,Ta[Qa],Ea,sa,Da);else if(2==Ea){for(var ea=e.BLKSIZE-1;0<=ea;--ea){var Jb=Oa[pb+0][ea],Bb=Oa[pb+1][ea];Oa[pb+0][ea]=0.5*(Jb+Bb)*X.SQRT2;Oa[pb+1][ea]=0.5*(Jb-Bb)*X.SQRT2}for(var Fa=2;0<=Fa;--Fa)for(ea=e.BLKSIZE_s-1;0<=ea;--ea)Jb=Ta[Qa+0][Fa][ea],Bb=Ta[Qa+1][Fa][ea],Ta[Qa+0][Fa][ea]=
0.5*(Jb+Bb)*X.SQRT2,Ta[Qa+1][Fa][ea]=0.5*(Jb-Bb)*X.SQRT2}Ja[0]=Oa[pb+0][0];Ja[0]*=Ja[0];for(ea=e.BLKSIZE/2-1;0<=ea;--ea){var Tb=Oa[pb+0][e.BLKSIZE/2-ea],ub=Oa[pb+0][e.BLKSIZE/2+ea];Ja[e.BLKSIZE/2-ea]=0.5*(Tb*Tb+ub*ub)}for(Fa=2;0<=Fa;--Fa){Ib[Fa][0]=Ta[Qa+0][Fa][0];Ib[Fa][0]*=Ib[Fa][0];for(ea=e.BLKSIZE_s/2-1;0<=ea;--ea)Tb=Ta[Qa+0][Fa][e.BLKSIZE_s/2-ea],ub=Ta[Qa+0][Fa][e.BLKSIZE_s/2+ea],Ib[Fa][e.BLKSIZE_s/2-ea]=0.5*(Tb*Tb+ub*ub)}for(var wa=0,ea=11;ea<e.HBLKSIZE;ea++)wa+=Ja[ea];Va.tot_ener[Ea]=wa;if(P.analysis){for(ea=
0;ea<e.HBLKSIZE;ea++)Va.pinfo.energy[hb][Ea][ea]=Va.pinfo.energy_save[Ea][ea],Va.pinfo.energy_save[Ea][ea]=Ja[ea];Va.pinfo.pe[hb][Ea]=Va.pe[Ea]}2==P.athaa_loudapprox&&2>Ea&&(Va.loudness_sq[hb][Ea]=Va.loudness_sq_save[Ea],Va.loudness_sq_save[Ea]=v(Ja,Va));b(j,va,s,la,za);n(j,la,za,W);for(Z=0;3>Z;Z++){for(var ic,ua,ab=$a,bb=C,Kb=y,Cb=I,Ub=Z,Y=a.internal_flags,ib=void 0,ja=void 0,ja=ib=0;ja<Y.npart_s;++ja){for(var Wa=0,cc=0,gb=Y.numlines_s[ja],cb=0;cb<gb;++cb,++ib){var jc=ab[Ub][ib],Wa=Wa+jc;cc<jc&&
(cc=jc)}bb[ja]=Wa}for(ib=ja=0;ja<Y.npart_s;ja++){var jb=Y.s3ind_s[ja][0],vb=Y.s3_ss[ib++]*bb[jb];for(++jb;jb<=Y.s3ind_s[ja][1];)vb+=Y.s3_ss[ib]*bb[jb],++ib,++jb;var Lb=2*Y.nb_s1[Cb][ja];Kb[ja]=Math.min(vb,Lb);Y.blocktype_old[Cb&1]==e.SHORT_TYPE&&(Lb=16*Y.nb_s2[Cb][ja],Kb[ja]=Math.min(Lb,Kb[ja]));Y.nb_s2[Cb][ja]=Y.nb_s1[Cb][ja];Y.nb_s1[Cb][ja]=vb}for(;ja<=e.CBANDS;++ja)bb[ja]=0,Kb[ja]=0;z(j,C,y,I,Z);for(N=0;N<e.SBMAX_s;N++){ua=j.thm[I].s[N][Z];ua*=0.8;if(2<=na[Z]||1==na[Z+1]){var Db=0!=Z?Z-1:2,Na=
u(j.thm[I].s[N][Db],ua,0.6*Q);ua=Math.min(ua,Na)}if(1==na[Z])Db=0!=Z?Z-1:2,Na=u(j.thm[I].s[N][Db],ua,0.3*Q),ua=Math.min(ua,Na);else if(0!=Z&&3==na[Z-1]||0==Z&&3==j.nsPsy.lastAttacks[I])Db=2!=Z?Z+1:0,Na=u(j.thm[I].s[N][Db],ua,0.3*Q),ua=Math.min(ua,Na);ic=ma[3*Z+3]+ma[3*Z+4]+ma[3*Z+5];6*ma[3*Z+5]<ic&&(ua*=0.5,6*ma[3*Z+4]<ic&&(ua*=0.5));j.thm[I].s[N][Z]=ua}}j.nsPsy.lastAttacks[I]=na[2];for(S=Ca=0;S<j.npart_l;S++){for(var kb=j.s3ind[S][0],Mb=s[kb]*t[W[kb]],lb=j.s3_ll[Ca++]*Mb;++kb<=j.s3ind[S][1];)Mb=
s[kb]*t[W[kb]],lb=p(lb,j.s3_ll[Ca++]*Mb,kb,kb-S,j,0);lb*=0.158489319246111;y[S]=j.blocktype_old[I&1]==e.SHORT_TYPE?lb:u(Math.min(lb,Math.min(2*j.nb_1[I][S],16*j.nb_2[I][S])),lb,Q);j.nb_2[I][S]=j.nb_1[I][S];j.nb_1[I][S]=lb}for(;S<=e.CBANDS;++S)s[S]=0,y[S]=0;x(j,s,y,I)}if((a.mode==ia.STEREO||a.mode==ia.JOINT_STEREO)&&0<a.interChRatio){var wb=a.interChRatio,$=a.internal_flags;if(1<$.channels_out){for(var Ga=0;Ga<e.SBMAX_l;Ga++){var Vb=$.thm[0].l[Ga],Eb=$.thm[1].l[Ga];$.thm[0].l[Ga]+=Eb*wb;$.thm[1].l[Ga]+=
Vb*wb}for(Ga=0;Ga<e.SBMAX_s;Ga++)for(var mb=0;3>mb;mb++)Vb=$.thm[0].s[Ga][mb],Eb=$.thm[1].s[Ga][mb],$.thm[0].s[Ga][mb]+=Eb*wb,$.thm[1].s[Ga][mb]+=Vb*wb}}if(a.mode==ia.JOINT_STEREO){for(var Ra,fa=0;fa<e.SBMAX_l;fa++)if(!(j.thm[0].l[fa]>1.58*j.thm[1].l[fa]||j.thm[1].l[fa]>1.58*j.thm[0].l[fa])){var Ua=j.mld_l[fa]*j.en[3].l[fa],nb=Math.max(j.thm[2].l[fa],Math.min(j.thm[3].l[fa],Ua)),Ua=j.mld_l[fa]*j.en[2].l[fa],kc=Math.max(j.thm[3].l[fa],Math.min(j.thm[2].l[fa],Ua));j.thm[2].l[fa]=nb;j.thm[3].l[fa]=kc}for(fa=
0;fa<e.SBMAX_s;fa++)for(var oa=0;3>oa;oa++)j.thm[0].s[fa][oa]>1.58*j.thm[1].s[fa][oa]||j.thm[1].s[fa][oa]>1.58*j.thm[0].s[fa][oa]||(Ua=j.mld_s[fa]*j.en[3].s[fa][oa],nb=Math.max(j.thm[2].s[fa][oa],Math.min(j.thm[3].s[fa][oa],Ua)),Ua=j.mld_s[fa]*j.en[2].s[fa][oa],kc=Math.max(j.thm[3].s[fa][oa],Math.min(j.thm[2].s[fa][oa],Ua)),j.thm[2].s[fa][oa]=nb,j.thm[3].s[fa][oa]=kc);Ra=a.msfix;if(0<Math.abs(Ra)){for(var Fb=Ra,dc=Fb,Wb=Math.pow(10,a.ATHlower*j.ATH.adjust),Fb=2*Fb,dc=2*dc,ta=0;ta<e.SBMAX_l;ta++){var Nb,
Ka,Aa,Xa;Xa=j.ATH.cb_l[j.bm_l[ta]]*Wb;Nb=Math.min(Math.max(j.thm[0].l[ta],Xa),Math.max(j.thm[1].l[ta],Xa));Ka=Math.max(j.thm[2].l[ta],Xa);Aa=Math.max(j.thm[3].l[ta],Xa);if(Nb*Fb<Ka+Aa){var Xb=Nb*dc/(Ka+Aa);Ka*=Xb;Aa*=Xb}j.thm[2].l[ta]=Math.min(Ka,j.thm[2].l[ta]);j.thm[3].l[ta]=Math.min(Aa,j.thm[3].l[ta])}Wb*=e.BLKSIZE_s/e.BLKSIZE;for(ta=0;ta<e.SBMAX_s;ta++)for(var pa=0;3>pa;pa++)Xa=j.ATH.cb_s[j.bm_s[ta]]*Wb,Nb=Math.min(Math.max(j.thm[0].s[ta][pa],Xa),Math.max(j.thm[1].s[ta][pa],Xa)),Ka=Math.max(j.thm[2].s[ta][pa],
Xa),Aa=Math.max(j.thm[3].s[ta][pa],Xa),Nb*Fb<Ka+Aa&&(Xb=Nb*Fb/(Ka+Aa),Ka*=Xb,Aa*=Xb),j.thm[2].s[ta][pa]=Math.min(j.thm[2].s[ta][pa],Ka),j.thm[3].s[ta][pa]=Math.min(j.thm[3].s[ta][pa],Aa)}}var db=a.internal_flags;a.short_blocks==ya.short_block_coupled&&!(0!=E[0]&&0!=E[1])&&(E[0]=E[1]=0);for(var Ia=0;Ia<db.channels_out;Ia++)G[Ia]=e.NORM_TYPE,a.short_blocks==ya.short_block_dispensed&&(E[Ia]=1),a.short_blocks==ya.short_block_forced&&(E[Ia]=0),0!=E[Ia]?db.blocktype_old[Ia]==e.SHORT_TYPE&&(G[Ia]=e.STOP_TYPE):
(G[Ia]=e.SHORT_TYPE,db.blocktype_old[Ia]==e.NORM_TYPE&&(db.blocktype_old[Ia]=e.START_TYPE),db.blocktype_old[Ia]==e.STOP_TYPE&&(db.blocktype_old[Ia]=e.SHORT_TYPE)),l[Ia]=db.blocktype_old[Ia],db.blocktype_old[Ia]=G[Ia];for(I=0;I<L;I++){var Ob,eb=0,ba,xb;if(1<I){Ob=k;eb=-2;ba=e.NORM_TYPE;if(l[0]==e.SHORT_TYPE||l[1]==e.SHORT_TYPE)ba=e.SHORT_TYPE;xb=g[c][I-2]}else Ob=i,eb=0,ba=l[I],xb=f[c][I];Ob[eb+I]=ba==e.SHORT_TYPE?q(xb,j.masking_lower):h(xb,j.masking_lower);a.analysis&&(j.pinfo.pe[c][I]=Ob[eb+I])}return 0};
var U=[-1.730326E-17,-0.01703172,-1.349528E-17,0.0418072,-6.73278E-17,-0.0876324,-3.0835E-17,0.1863476,-1.104424E-16,-0.627638];this.L3psycho_anal_vbr=function(d,m,c,f,g,i,k,j,w,p){for(var r=d.internal_flags,s,A,C=K(e.HBLKSIZE),y=qa([3,e.HBLKSIZE_s]),G=qa([2,e.BLKSIZE]),R=qa([2,3,e.BLKSIZE_s]),E=qa([4,e.CBANDS]),L=qa([4,e.CBANDS]),I=qa([4,3]),F=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]],H=T(2),J=d.mode==ia.JOINT_STEREO?4:r.channels_out,N=qa([2,576]),Ca=d.internal_flags,Z=Ca.channels_out,aa=d.mode==
ia.JOINT_STEREO?4:Z,Q=0;Q<Z;Q++){firbuf=m[Q];for(var W=c+576-350-21+192,M=0;576>M;M++){var tb,ka;tb=firbuf[W+M+10];for(var ca=ka=0;9>ca;ca+=2)tb+=U[ca]*(firbuf[W+M+ca]+firbuf[W+M+21-ca]),ka+=U[ca+1]*(firbuf[W+M+ca+1]+firbuf[W+M+21-ca-1]);N[Q][M]=tb+ka}g[f][Q].en.assign(Ca.en[Q]);g[f][Q].thm.assign(Ca.thm[Q]);2<aa&&(i[f][Q].en.assign(Ca.en[Q+2]),i[f][Q].thm.assign(Ca.thm[Q+2]))}for(Q=0;Q<aa;Q++){var ra=K(12),ma=K(12),Pa=[0,0,0,0],xa=N[Q&1],Ba=0,la=3==Q?Ca.nsPsy.attackthre_s:Ca.nsPsy.attackthre,Ha=
1;if(2==Q){M=0;for(ca=576;0<ca;++M,--ca){var za=N[0][M],na=N[1][M];N[0][M]=za+na;N[1][M]=za-na}}for(M=0;3>M;M++)ma[M]=Ca.nsPsy.last_en_subshort[Q][M+6],ra[M]=ma[M]/Ca.nsPsy.last_en_subshort[Q][M+4],Pa[0]+=ma[M];for(M=0;9>M;M++){for(var va=Ba+64,$a=1;Ba<va;Ba++)$a<Math.abs(xa[Ba])&&($a=Math.abs(xa[Ba]));Ca.nsPsy.last_en_subshort[Q][M]=ma[M+3]=$a;Pa[1+M/3]+=$a;$a=$a>ma[M+3-2]?$a/ma[M+3-2]:ma[M+3-2]>10*$a?ma[M+3-2]/(10*$a):0;ra[M+3]=$a}for(M=0;3>M;++M){var Ma=ma[3*M+3]+ma[3*M+4]+ma[3*M+5],zb=1;6*ma[3*
M+5]<Ma&&(zb*=0.5,6*ma[3*M+4]<Ma&&(zb*=0.5));I[Q][M]=zb}if(d.analysis){for(var Da=ra[0],M=1;12>M;M++)Da<ra[M]&&(Da=ra[M]);Ca.pinfo.ers[f][Q]=Ca.pinfo.ers_save[Q];Ca.pinfo.ers_save[Q]=Da}for(M=0;12>M;M++)0==F[Q][M/3]&&ra[M]>la&&(F[Q][M/3]=M%3+1);for(M=1;4>M;M++){var Ab=Pa[M-1],La=Pa[M];4E4>Math.max(Ab,La)&&(Ab<1.7*La&&La<1.7*Ab)&&(1==M&&F[Q][0]<=F[Q][M]&&(F[Q][0]=0),F[Q][M]=0)}F[Q][0]<=Ca.nsPsy.lastAttacks[Q]&&(F[Q][0]=0);if(3==Ca.nsPsy.lastAttacks[Q]||0!=F[Q][0]+F[Q][1]+F[Q][2]+F[Q][3])Ha=0,0!=F[Q][1]&&
0!=F[Q][0]&&(F[Q][1]=0),0!=F[Q][2]&&0!=F[Q][1]&&(F[Q][2]=0),0!=F[Q][3]&&0!=F[Q][2]&&(F[Q][3]=0);2>Q?H[Q]=Ha:0==Ha&&(H[0]=H[1]=0);w[Q]=Ca.tot_ener[Q]}var Na=d.internal_flags;d.short_blocks==ya.short_block_coupled&&!(0!=H[0]&&0!=H[1])&&(H[0]=H[1]=0);for(var Hb=0;Hb<Na.channels_out;Hb++)d.short_blocks==ya.short_block_dispensed&&(H[Hb]=1),d.short_blocks==ya.short_block_forced&&(H[Hb]=0);for(var P=0;P<J;P++){var Ja=P&1;s=G;var Ib=d,Oa=P,pb=f,Ta=C,Qa=s,hb=Ja,Ea=Ib.internal_flags;if(2>Oa)B.fft_long(Ea,Qa[hb],
Oa,m,c);else if(2==Oa)for(var sa=e.BLKSIZE-1;0<=sa;--sa){var gb=Qa[hb+0][sa],Va=Qa[hb+1][sa];Qa[hb+0][sa]=0.5*(gb+Va)*X.SQRT2;Qa[hb+1][sa]=0.5*(gb-Va)*X.SQRT2}Ta[0]=Qa[hb+0][0];Ta[0]*=Ta[0];for(sa=e.BLKSIZE/2-1;0<=sa;--sa){var ea=Qa[hb+0][e.BLKSIZE/2-sa],Jb=Qa[hb+0][e.BLKSIZE/2+sa];Ta[e.BLKSIZE/2-sa]=0.5*(ea*ea+Jb*Jb)}for(var Bb=0,sa=11;sa<e.HBLKSIZE;sa++)Bb+=Ta[sa];Ea.tot_ener[Oa]=Bb;if(Ib.analysis){for(sa=0;sa<e.HBLKSIZE;sa++)Ea.pinfo.energy[pb][Oa][sa]=Ea.pinfo.energy_save[Oa][sa],Ea.pinfo.energy_save[Oa][sa]=
Ta[sa];Ea.pinfo.pe[pb][Oa]=Ea.pe[Oa]}var Fa=P,Tb=C,ub=d.internal_flags;2==d.athaa_loudapprox&&2>Fa&&(ub.loudness_sq[f][Fa]=ub.loudness_sq_save[Fa],ub.loudness_sq_save[Fa]=v(Tb,ub));if(0!=H[Ja]){var wa=r,ic=C,ua=E[P],ab=L[P],bb=P,Kb=K(e.CBANDS),Cb=K(e.CBANDS),Ub=T(e.CBANDS+2),Y=void 0;b(wa,ic,ua,Kb,Cb);n(wa,Kb,Cb,Ub);for(var ib=0,Y=0;Y<wa.npart_l;Y++){var ja,Wa,cc,bc,cb=wa.s3ind[Y][0],jc=wa.s3ind[Y][1],jb=0,vb=0,jb=Ub[cb],vb=vb+1;Wa=wa.s3_ll[ib]*ua[cb]*t[Ub[cb]];++ib;for(++cb;cb<=jc;)jb+=Ub[cb],vb+=
1,ja=wa.s3_ll[ib]*ua[cb]*t[Ub[cb]],Wa=bc=l(Wa,ja,cb-Y),++ib,++cb;jb=(1+2*jb)/(2*vb);cc=0.5*t[jb];Wa*=cc;if(wa.blocktype_old[bb&1]==e.SHORT_TYPE){var Lb=2*wa.nb_1[bb][Y];ab[Y]=0<Lb?Math.min(Wa,Lb):Math.min(Wa,0.3*ua[Y])}else{var Db=16*wa.nb_2[bb][Y],kb=2*wa.nb_1[bb][Y];0>=Db&&(Db=Wa);0>=kb&&(kb=Wa);Lb=wa.blocktype_old[bb&1]==e.NORM_TYPE?Math.min(kb,Db):kb;ab[Y]=Math.min(Wa,Lb)}wa.nb_2[bb][Y]=wa.nb_1[bb][Y];wa.nb_1[bb][Y]=Wa;ja=Kb[Y];ja*=wa.minval_l[Y];ja*=cc;ab[Y]>ja&&(ab[Y]=ja);1<wa.masking_lower&&
(ab[Y]*=wa.masking_lower);ab[Y]>ua[Y]&&(ab[Y]=ua[Y]);1>wa.masking_lower&&(ab[Y]*=wa.masking_lower)}for(;Y<e.CBANDS;++Y)ua[Y]=0,ab[Y]=0}else for(var Mb=r,lb=P,wb=0;wb<Mb.npart_l;wb++)Mb.nb_2[lb][wb]=Mb.nb_1[lb][wb],Mb.nb_1[lb][wb]=0}2==H[0]+H[1]&&d.mode==ia.JOINT_STEREO&&a(E,L,r.mld_cb_l,r.ATH.cb_l,d.ATHlower*r.ATH.adjust,d.msfix,r.npart_l);for(P=0;P<J;P++)Ja=P&1,0!=H[Ja]&&x(r,E[P],L[P],P);for(var $=0;3>$;$++){for(P=0;P<J;++P)if(Ja=P&1,0!=H[Ja]){var Ga=r,Vb=P;if(0==$)for(var Eb=0;Eb<Ga.npart_s;Eb++)Ga.nb_s2[Vb][Eb]=
Ga.nb_s1[Vb][Eb],Ga.nb_s1[Vb][Eb]=0}else{A=R;var mb=P,Ra=$,fa=y,Ua=A,nb=Ja,kc=d.internal_flags;0==Ra&&2>mb&&B.fft_short(kc,Ua[nb],mb,m,c);if(2==mb)for(var oa=e.BLKSIZE_s-1;0<=oa;--oa){var Fb=Ua[nb+0][Ra][oa],dc=Ua[nb+1][Ra][oa];Ua[nb+0][Ra][oa]=0.5*(Fb+dc)*X.SQRT2;Ua[nb+1][Ra][oa]=0.5*(Fb-dc)*X.SQRT2}fa[Ra][0]=Ua[nb+0][Ra][0];fa[Ra][0]*=fa[Ra][0];for(oa=e.BLKSIZE_s/2-1;0<=oa;--oa){var Wb=Ua[nb+0][Ra][e.BLKSIZE_s/2-oa],ta=Ua[nb+0][Ra][e.BLKSIZE_s/2+oa];fa[Ra][e.BLKSIZE_s/2-oa]=0.5*(Wb*Wb+ta*ta)}for(var Nb=
y,Ka=E[P],Aa=L[P],Xa=P,Xb=$,pa=d.internal_flags,db=new float[e.CBANDS],Ia=K(e.CBANDS),Ob=void 0,eb=void 0,ba=void 0,xb=new int[e.CBANDS],ba=eb=0;ba<pa.npart_s;++ba){for(var Za=0,Rb=0,ac=pa.numlines_s[ba],Ob=0;Ob<ac;++Ob,++eb){var Sb=Nb[Xb][eb],Za=Za+Sb;Rb<Sb&&(Rb=Sb)}Ka[ba]=Za;db[ba]=Rb;Ia[ba]=Za/ac}for(;ba<e.CBANDS;++ba)db[ba]=0,Ia[ba]=0;var Pb=pa,qb=db,Yb=Ia,ec=xb,fc=t.length-1,ha=0,Sa=Yb[ha]+Yb[ha+1];if(0<Sa){var fb=qb[ha];fb<qb[ha+1]&&(fb=qb[ha+1]);var Sa=20*(2*fb-Sa)/(Sa*(Pb.numlines_s[ha]+Pb.numlines_s[ha+
1]-1)),rb=0|Sa;rb>fc&&(rb=fc);ec[ha]=rb}else ec[ha]=0;for(ha=1;ha<Pb.npart_s-1;ha++)Sa=Yb[ha-1]+Yb[ha]+Yb[ha+1],0<Sa?(fb=qb[ha-1],fb<qb[ha]&&(fb=qb[ha]),fb<qb[ha+1]&&(fb=qb[ha+1]),Sa=20*(3*fb-Sa)/(Sa*(Pb.numlines_s[ha-1]+Pb.numlines_s[ha]+Pb.numlines_s[ha+1]-1)),rb=0|Sa,rb>fc&&(rb=fc),ec[ha]=rb):ec[ha]=0;Sa=Yb[ha-1]+Yb[ha];0<Sa?(fb=qb[ha-1],fb<qb[ha]&&(fb=qb[ha]),Sa=20*(2*fb-Sa)/(Sa*(Pb.numlines_s[ha-1]+Pb.numlines_s[ha]-1)),rb=0|Sa,rb>fc&&(rb=fc),ec[ha]=rb):ec[ha]=0;for(eb=ba=0;ba<pa.npart_s;ba++){var yb=
pa.s3ind_s[ba][0],nc=pa.s3ind_s[ba][1],gc,pc,Gb,Zb,uc;gc=xb[yb];pc=1;Zb=pa.s3_ss[eb]*Ka[yb]*t[xb[yb]];++eb;for(++yb;yb<=nc;)gc+=xb[yb],pc+=1,Gb=pa.s3_ss[eb]*Ka[yb]*t[xb[yb]],Zb=l(Zb,Gb,yb-ba),++eb,++yb;gc=(1+2*gc)/(2*pc);uc=0.5*t[gc];Zb*=uc;Aa[ba]=Zb;pa.nb_s2[Xa][ba]=pa.nb_s1[Xa][ba];pa.nb_s1[Xa][ba]=Zb;Gb=db[ba];Gb*=pa.minval_s[ba];Gb*=uc;Aa[ba]>Gb&&(Aa[ba]=Gb);1<pa.masking_lower&&(Aa[ba]*=pa.masking_lower);Aa[ba]>Ka[ba]&&(Aa[ba]=Ka[ba]);1>pa.masking_lower&&(Aa[ba]*=pa.masking_lower)}for(;ba<e.CBANDS;++ba)Ka[ba]=
0,Aa[ba]=0}0==H[0]+H[1]&&d.mode==ia.JOINT_STEREO&&a(E,L,r.mld_cb_s,r.ATH.cb_s,d.ATHlower*r.ATH.adjust,d.msfix,r.npart_s);for(P=0;P<J;++P)Ja=P&1,0==H[Ja]&&z(r,E[P],L[P],P,$)}for(P=0;P<J;P++)if(Ja=P&1,0==H[Ja])for(var $b=0;$b<e.SBMAX_s;$b++){for(var zc=K(3),$=0;3>$;$++){var Ya=r.thm[P].s[$b][$],Ya=0.8*Ya;if(2<=F[P][$]||1==F[P][$+1])var lc=0!=$?$-1:2,mc=u(r.thm[P].s[$b][lc],Ya,0.36),Ya=Math.min(Ya,mc);else if(1==F[P][$])lc=0!=$?$-1:2,mc=u(r.thm[P].s[$b][lc],Ya,0.18),Ya=Math.min(Ya,mc);else if(0!=$&&
3==F[P][$-1]||0==$&&3==r.nsPsy.lastAttacks[P])lc=2!=$?$+1:0,mc=u(r.thm[P].s[$b][lc],Ya,0.18),Ya=Math.min(Ya,mc);Ya*=I[P][$];zc[$]=Ya}for($=0;3>$;$++)r.thm[P].s[$b][$]=zc[$]}for(P=0;P<J;P++)r.nsPsy.lastAttacks[P]=F[P][2];for(var Qb=d.internal_flags,sb=0;sb<Qb.channels_out;sb++){var vc=e.NORM_TYPE;0!=H[sb]?Qb.blocktype_old[sb]==e.SHORT_TYPE&&(vc=e.STOP_TYPE):(vc=e.SHORT_TYPE,Qb.blocktype_old[sb]==e.NORM_TYPE&&(Qb.blocktype_old[sb]=e.START_TYPE),Qb.blocktype_old[sb]==e.STOP_TYPE&&(Qb.blocktype_old[sb]=
e.SHORT_TYPE));p[sb]=Qb.blocktype_old[sb];Qb.blocktype_old[sb]=vc}for(P=0;P<J;P++){var qc,rc,sc,tc;if(1<P){qc=j;rc=-2;sc=e.NORM_TYPE;if(p[0]==e.SHORT_TYPE||p[1]==e.SHORT_TYPE)sc=e.SHORT_TYPE;tc=i[f][P-2]}else qc=k,rc=0,sc=p[P],tc=g[f][P];qc[rc+P]=sc==e.SHORT_TYPE?q(tc,r.masking_lower):h(tc,r.masking_lower);d.analysis&&(r.pinfo.pe[f][P]=qc[rc+P])}return 0};this.psymodel_init=function(a){var b=a.internal_flags,c,f=!0,h=13,j=0,t=0,l=-8.25,p=-4.5,n=K(e.CBANDS),q=K(e.CBANDS),r=K(e.CBANDS),s=a.out_samplerate;
switch(a.experimentalZ){default:case 0:f=!0;break;case 1:f=a.VBR==F.vbr_mtrh||a.VBR==F.vbr_mt?!1:!0;break;case 2:f=!1;break;case 3:h=8,j=-1.75,t=-0.0125,l=-8.25,p=-2.25}b.ms_ener_ratio_old=0.25;b.blocktype_old[0]=b.blocktype_old[1]=e.NORM_TYPE;for(c=0;4>c;++c){for(var A=0;A<e.CBANDS;++A)b.nb_1[c][A]=1E20,b.nb_2[c][A]=1E20,b.nb_s1[c][A]=b.nb_s2[c][A]=1;for(var u=0;u<e.SBMAX_l;u++)b.en[c].l[u]=1E20,b.thm[c].l[u]=1E20;for(A=0;3>A;++A){for(u=0;u<e.SBMAX_s;u++)b.en[c].s[u][A]=1E20,b.thm[c].s[u][A]=1E20;
b.nsPsy.lastAttacks[c]=0}for(A=0;9>A;A++)b.nsPsy.last_en_subshort[c][A]=10}b.loudness_sq_save[0]=b.loudness_sq_save[1]=0;b.npart_l=k(b.numlines_l,b.bo_l,b.bm_l,n,q,b.mld_l,b.PSY.bo_l_weight,s,e.BLKSIZE,b.scalefac_band.l,e.BLKSIZE/1152,e.SBMAX_l);for(c=0;c<b.npart_l;c++)u=j,n[c]>=h&&(u=t*(n[c]-h)/(24-h)+j*(24-n[c])/(24-h)),r[c]=Math.pow(10,u/10),b.rnumlines_l[c]=0<b.numlines_l[c]?1/b.numlines_l[c]:0;b.s3_ll=g(b.s3ind,b.npart_l,n,q,r,f);for(c=A=0;c<b.npart_l;c++){t=Ma.MAX_VALUE;for(u=0;u<b.numlines_l[c];u++,
A++)j=s*A/(1E3*e.BLKSIZE),j=this.ATHformula(1E3*j,a)-20,j=Math.pow(10,0.1*j),j*=b.numlines_l[c],t>j&&(t=j);b.ATH.cb_l[c]=t;t=-20+20*n[c]/10;6<t&&(t=100);-15>t&&(t=-15);t-=8;b.minval_l[c]=Math.pow(10,t/10)*b.numlines_l[c]}b.npart_s=k(b.numlines_s,b.bo_s,b.bm_s,n,q,b.mld_s,b.PSY.bo_s_weight,s,e.BLKSIZE_s,b.scalefac_band.s,e.BLKSIZE_s/384,e.SBMAX_s);for(c=A=0;c<b.npart_s;c++){u=l;n[c]>=h&&(u=p*(n[c]-h)/(24-h)+l*(24-n[c])/(24-h));r[c]=Math.pow(10,u/10);t=Ma.MAX_VALUE;for(u=0;u<b.numlines_s[c];u++,A++)j=
s*A/(1E3*e.BLKSIZE_s),j=this.ATHformula(1E3*j,a)-20,j=Math.pow(10,0.1*j),j*=b.numlines_s[c],t>j&&(t=j);b.ATH.cb_s[c]=t;t=-7+7*n[c]/12;12<n[c]&&(t*=1+3.1*Math.log(1+t));12>n[c]&&(t*=1+2.3*Math.log(1-t));-15>t&&(t=-15);t-=8;b.minval_s[c]=Math.pow(10,t/10)*b.numlines_s[c]}b.s3_ss=g(b.s3ind_s,b.npart_s,n,q,r,f);i=Math.pow(10,0.5625);m=Math.pow(10,1.5);w=Math.pow(10,1.5);B.init_fft(b);b.decay=Math.exp(-1*y/(0.01*s/192));c=3.5;0!=(a.exp_nspsytune&2)&&(c=1);0<Math.abs(a.msfix)&&(c=a.msfix);a.msfix=c;for(f=
0;f<b.npart_l;f++)b.s3ind[f][1]>b.npart_l-1&&(b.s3ind[f][1]=b.npart_l-1);b.ATH.decay=Math.pow(10,-1.2*(576*b.mode_gr/s));b.ATH.adjust=0.01;b.ATH.adjustLimit=1;if(-1!=a.ATHtype){A=a.out_samplerate/e.BLKSIZE;for(c=j=f=0;c<e.BLKSIZE/2;++c)j+=A,b.ATH.eql_w[c]=1/Math.pow(10,this.ATHformula(j,a)/10),f+=b.ATH.eql_w[c];f=1/f;for(c=e.BLKSIZE/2;0<=--c;)b.ATH.eql_w[c]*=f}for(f=A=0;f<b.npart_s;++f)for(c=0;c<b.numlines_s[f];++c)++A;for(f=A=0;f<b.npart_l;++f)for(c=0;c<b.numlines_l[f];++c)++A;for(c=A=0;c<b.npart_l;c++)j=
s*(A+b.numlines_l[c]/2)/(1*e.BLKSIZE),b.mld_cb_l[c]=d(j),A+=b.numlines_l[c];for(;c<e.CBANDS;++c)b.mld_cb_l[c]=1;for(c=A=0;c<b.npart_s;c++)j=s*(A+b.numlines_s[c]/2)/(1*e.BLKSIZE_s),b.mld_cb_s[c]=d(j),A+=b.numlines_s[c];for(;c<e.CBANDS;++c)b.mld_cb_s[c]=1;return 0};this.ATHformula=function(a,b){var d;switch(b.ATHtype){case 0:d=c(a,9);break;case 1:d=c(a,-1);break;case 2:d=c(a,0);break;case 3:d=c(a,1)+6;break;case 4:d=c(a,b.ATHcurve);break;default:d=c(a,0)}return d}}function E(){function D(){this.mask_adjust_short=
this.mask_adjust=0;this.bo_l_weight=K(e.SBMAX_l);this.bo_s_weight=K(e.SBMAX_s)}function p(){this.lowerlimit=0}function l(a,b){this.lowpass=b}function z(a,b){switch(a){case 44100:return b.version=1,0;case 48E3:return b.version=1;case 32E3:return b.version=1,2;case 22050:return b.version=0;case 24E3:return b.version=0,1;case 16E3:return b.version=0,2;case 11025:return b.version=0;case 12E3:return b.version=0,1;case 8E3:return b.version=0,2;default:return b.version=0,-1}}function x(a,b,c){16E3>c&&(b=
2);for(var c=v.bitrate_table[b][1],d=2;14>=d;d++)0<v.bitrate_table[b][d]&&Math.abs(v.bitrate_table[b][d]-a)<Math.abs(c-a)&&(c=v.bitrate_table[b][d]);return c}function u(a,b,c){16E3>c&&(b=2);for(c=0;14>=c;c++)if(0<v.bitrate_table[b][c]&&v.bitrate_table[b][c]==a)return c;return-1}function q(a,b){var c=[new l(8,2E3),new l(16,3700),new l(24,3900),new l(32,5500),new l(40,7E3),new l(48,7500),new l(56,1E4),new l(64,11E3),new l(80,13500),new l(96,15100),new l(112,15600),new l(128,17E3),new l(160,17500),new l(192,
18600),new l(224,19400),new l(256,19700),new l(320,20500)],d=J.nearestBitrateFullIndex(b);a.lowerlimit=c[d].lowpass}function h(a){var b=e.BLKSIZE+a.framesize-e.FFTOFFSET;return b=Math.max(b,512+a.framesize-32)}function b(b,c,d,j,l,p,q){var r=b.internal_flags,u=0,s,v,x=[null,null],z=[null,null];if(r.Class_ID!=i)return-3;if(0==j)return 0;s=g.copy_buffer(r,l,p,q,0);if(0>s)return s;p+=s;u+=s;z[0]=c;z[1]=d;if(ka.NEQ(b.scale,0)&&ka.NEQ(b.scale,1))for(s=0;s<j;++s)z[0][s]*=b.scale,2==r.channels_out&&(z[1][s]*=
b.scale);if(ka.NEQ(b.scale_left,0)&&ka.NEQ(b.scale_left,1))for(s=0;s<j;++s)z[0][s]*=b.scale_left;if(ka.NEQ(b.scale_right,0)&&ka.NEQ(b.scale_right,1))for(s=0;s<j;++s)z[1][s]*=b.scale_right;if(2==b.num_channels&&1==r.channels_out)for(s=0;s<j;++s)z[0][s]=0.5*(z[0][s]+z[1][s]),z[1][s]=0;c=h(b);x[0]=r.mfbuf[0];x[1]=r.mfbuf[1];for(d=0;0<j;){var y=[null,null];s=v=0;y[0]=z[0];y[1]=z[1];s=new n;var D=b;v=x;var B=d,E=j,F=s,N=D.internal_flags;if(0.9999>N.resample_ratio||1.0001<N.resample_ratio)for(var T=0;T<
N.channels_out;T++){var W=new a,X=F,ca=v[T],ia=N.mf_size,qa=D.framesize,ra=y[T],ya=B,Ba=E,xa=W,Ha=T,I=D.internal_flags,S=void 0,H=0,la=void 0,za=D.out_samplerate/f(D.out_samplerate,D.in_samplerate);za>aa.BPC&&(za=aa.BPC);var va=1E-4>Math.abs(I.resample_ratio-Math.floor(0.5+I.resample_ratio))?1:0,la=1/I.resample_ratio;1<la&&(la=1);var Z=31;0==Z%2&&--Z;Z+=va;va=Z+1;if(0==I.fill_buffer_resample_init){I.inbuf_old[0]=K(va);I.inbuf_old[1]=K(va);for(S=0;S<=2*za;++S)I.blackfilt[S]=K(va);I.itime[0]=0;for(H=
I.itime[1]=0;H<=2*za;H++){for(var Da=0,Q=(H-za)/(2*za),S=0;S<=Z;S++){var Ma=I.blackfilt[H],M=S,La;var ob=S-Q;La=Z;var gb=Math.PI*la,ob=ob/La;0>ob&&(ob=0);1<ob&&(ob=1);var Za=ob-0.5,ob=0.42-0.5*Math.cos(2*ob*Math.PI)+0.08*Math.cos(4*ob*Math.PI);La=1E-9>Math.abs(Za)?gb/Math.PI:ob*Math.sin(La*gb*Za)/(Math.PI*La*Za);Da+=Ma[M]=La}for(S=0;S<=Z;S++)I.blackfilt[H][S]/=Da}I.fill_buffer_resample_init=1}Da=I.inbuf_old[Ha];for(la=0;la<qa;la++){S=la*I.resample_ratio;H=0|Math.floor(S-I.itime[Ha]);if(Z+H-Z/2>=Ba)break;
Q=S-I.itime[Ha]-(H+0.5*(Z%2));Q=0|Math.floor(2*Q*za+za+0.5);for(S=Ma=0;S<=Z;++S)M=S+H-Z/2,Ma+=(0>M?Da[va+M]:ra[ya+M])*I.blackfilt[Q][S];ca[ia+la]=Ma}xa.num_used=Math.min(Ba,Z+H-Z/2);I.itime[Ha]+=xa.num_used-la*I.resample_ratio;if(xa.num_used>=va)for(S=0;S<va;S++)Da[S]=ra[ya+xa.num_used+S-va];else{ca=va-xa.num_used;for(S=0;S<ca;++S)Da[S]=Da[S+xa.num_used];for(H=0;S<va;++S,++H)Da[S]=ra[ya+H]}X.n_out=la;F.n_in=W.num_used}else{F.n_out=Math.min(D.framesize,E);F.n_in=F.n_out;for(D=0;D<F.n_out;++D)v[0][N.mf_size+
D]=y[0][B+D],2==N.channels_out&&(v[1][N.mf_size+D]=y[1][B+D])}v=s.n_in;s=s.n_out;if(r.findReplayGain&&!r.decode_on_the_fly&&k.AnalyzeSamples(r.rgdata,x[0],r.mf_size,x[1],r.mf_size,s,r.channels_out)==U.GAIN_ANALYSIS_ERROR)return-6;j-=v;d+=v;r.mf_size+=s;1>r.mf_samples_to_encode&&(r.mf_samples_to_encode=e.ENCDELAY+e.POSTDELAY);r.mf_samples_to_encode+=s;if(r.mf_size>=c){v=q-u;0==q&&(v=0);s=b;v=J.enc.lame_encode_mp3_frame(s,x[0],x[1],l,p,v);s.frameNum++;s=v;if(0>s)return s;p+=s;u+=s;r.mf_size-=b.framesize;
r.mf_samples_to_encode-=b.framesize;for(v=0;v<r.channels_out;v++)for(s=0;s<r.mf_size;s++)x[v][s]=x[v][s+b.framesize]}}return u}function n(){this.n_out=this.n_in=0}function a(){this.num_used=0}function f(a,b){return 0!=b?f(b,a%b):a}var J=this;E.V9=410;E.V8=420;E.V7=430;E.V6=440;E.V5=450;E.V4=460;E.V3=470;E.V2=480;E.V1=490;E.V0=500;E.R3MIX=1E3;E.STANDARD=1001;E.EXTREME=1002;E.INSANE=1003;E.STANDARD_FAST=1004;E.EXTREME_FAST=1005;E.MEDIUM=1006;E.MEDIUM_FAST=1007;E.LAME_MAXMP3BUFFER=147456;var k,g,d,c,
B,y=new Sb,r,s,j;this.enc=new e;this.setModules=function(a,b,e,f,i,h,l,p,n){k=a;g=b;d=e;c=f;B=i;r=h;s=p;j=n;this.enc.setModules(g,y,c,r)};var i=4294479419;this.lame_init=function(){var a=new Ec,b;a.class_id=i;b=a.internal_flags=new aa;a.mode=ia.NOT_SET;a.original=1;a.in_samplerate=44100;a.num_channels=2;a.num_samples=-1;a.bWriteVbrTag=!0;a.quality=-1;a.short_blocks=null;b.subblock_gain=-1;a.lowpassfreq=0;a.highpassfreq=0;a.lowpasswidth=-1;a.highpasswidth=-1;a.VBR=F.vbr_off;a.VBR_q=4;a.ATHcurve=-1;
a.VBR_mean_bitrate_kbps=128;a.VBR_min_bitrate_kbps=0;a.VBR_max_bitrate_kbps=0;a.VBR_hard_min=0;b.VBR_min_bitrate=1;b.VBR_max_bitrate=13;a.quant_comp=-1;a.quant_comp_short=-1;a.msfix=-1;b.resample_ratio=1;b.OldValue[0]=180;b.OldValue[1]=180;b.CurrentStep[0]=4;b.CurrentStep[1]=4;b.masking_lower=1;b.nsPsy.attackthre=-1;b.nsPsy.attackthre_s=-1;a.scale=-1;a.athaa_type=-1;a.ATHtype=-1;a.athaa_loudapprox=-1;a.athaa_sensitivity=0;a.useTemporal=null;a.interChRatio=-1;b.mf_samples_to_encode=e.ENCDELAY+e.POSTDELAY;
a.encoder_padding=0;b.mf_size=e.ENCDELAY-e.MDCTDELAY;a.findReplayGain=!1;a.decode_on_the_fly=!1;b.decode_on_the_fly=!1;b.findReplayGain=!1;b.findPeakSample=!1;b.RadioGain=0;b.AudiophileGain=0;b.noclipGainChange=0;b.noclipScale=-1;a.preset=0;a.write_id3tag_automatic=!0;a.lame_allocated_gfp=1;return a};this.nearestBitrateFullIndex=function(a){for(var b=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320],c=0,d=0,e=0,f=0,f=b[16],e=16,d=b[16],c=16,g=0;16>g;g++)if(Math.max(a,b[g+1])!=a){f=b[g+1];
e=g+1;d=b[g];c=g;break}return f-a>a-d?c:e};this.lame_init_params=function(a){var b=a.internal_flags;b.Class_ID=0;null==b.ATH&&(b.ATH=new Fc);null==b.PSY&&(b.PSY=new D);null==b.rgdata&&(b.rgdata=new Gc);b.channels_in=a.num_channels;1==b.channels_in&&(a.mode=ia.MONO);b.channels_out=a.mode==ia.MONO?1:2;b.mode_ext=e.MPG_MD_MS_LR;a.mode==ia.MONO&&(a.force_ms=!1);a.VBR==F.vbr_off&&(128!=a.VBR_mean_bitrate_kbps&&0==a.brate)&&(a.brate=a.VBR_mean_bitrate_kbps);a.VBR==F.vbr_off||(a.VBR==F.vbr_mtrh||a.VBR==
F.vbr_mt)||(a.free_format=!1);a.VBR==F.vbr_off&&0==a.brate&&ka.EQ(a.compression_ratio,0)&&(a.compression_ratio=11.025);a.VBR==F.vbr_off&&0<a.compression_ratio&&(0==a.out_samplerate&&(a.out_samplerate=map2MP3Frequency(int(0.97*a.in_samplerate))),a.brate=0|16*a.out_samplerate*b.channels_out/(1E3*a.compression_ratio),b.samplerate_index=z(a.out_samplerate,a),a.free_format||(a.brate=x(a.brate,a.version,a.out_samplerate)));0!=a.out_samplerate&&(16E3>a.out_samplerate?(a.VBR_mean_bitrate_kbps=Math.max(a.VBR_mean_bitrate_kbps,
8),a.VBR_mean_bitrate_kbps=Math.min(a.VBR_mean_bitrate_kbps,64)):32E3>a.out_samplerate?(a.VBR_mean_bitrate_kbps=Math.max(a.VBR_mean_bitrate_kbps,8),a.VBR_mean_bitrate_kbps=Math.min(a.VBR_mean_bitrate_kbps,160)):(a.VBR_mean_bitrate_kbps=Math.max(a.VBR_mean_bitrate_kbps,32),a.VBR_mean_bitrate_kbps=Math.min(a.VBR_mean_bitrate_kbps,320)));if(0==a.lowpassfreq){var f=16E3;switch(a.VBR){case F.vbr_off:f=new p;q(f,a.brate);f=f.lowerlimit;break;case F.vbr_abr:f=new p;q(f,a.VBR_mean_bitrate_kbps);f=f.lowerlimit;
break;case F.vbr_rh:var h=[19500,19E3,18600,18E3,17500,16E3,15600,14900,12500,1E4,3950];if(0<=a.VBR_q&&9>=a.VBR_q)var f=h[a.VBR_q],h=h[a.VBR_q+1],l=a.VBR_q_frac,f=linear_int(f,h,l);else f=19500;break;default:h=[19500,19E3,18500,18E3,17500,16500,15500,14500,12500,9500,3950],0<=a.VBR_q&&9>=a.VBR_q?(f=h[a.VBR_q],h=h[a.VBR_q+1],l=a.VBR_q_frac,f=linear_int(f,h,l)):f=19500}if(a.mode==ia.MONO&&(a.VBR==F.vbr_off||a.VBR==F.vbr_abr))f*=1.5;a.lowpassfreq=f|0}0==a.out_samplerate&&(2*a.lowpassfreq>a.in_samplerate&&
(a.lowpassfreq=a.in_samplerate/2),f=a.lowpassfreq|0,h=a.in_samplerate,l=44100,48E3<=h?l=48E3:44100<=h?l=44100:32E3<=h?l=32E3:24E3<=h?l=24E3:22050<=h?l=22050:16E3<=h?l=16E3:12E3<=h?l=12E3:11025<=h?l=11025:8E3<=h&&(l=8E3),-1==f?f=l:(15960>=f&&(l=44100),15250>=f&&(l=32E3),11220>=f&&(l=24E3),9970>=f&&(l=22050),7230>=f&&(l=16E3),5420>=f&&(l=12E3),4510>=f&&(l=11025),3970>=f&&(l=8E3),f=h<l?44100<h?48E3:32E3<h?44100:24E3<h?32E3:22050<h?24E3:16E3<h?22050:12E3<h?16E3:11025<h?12E3:8E3<h?11025:8E3:l),a.out_samplerate=
f);a.lowpassfreq=Math.min(20500,a.lowpassfreq);a.lowpassfreq=Math.min(a.out_samplerate/2,a.lowpassfreq);a.VBR==F.vbr_off&&(a.compression_ratio=16*a.out_samplerate*b.channels_out/(1E3*a.brate));a.VBR==F.vbr_abr&&(a.compression_ratio=16*a.out_samplerate*b.channels_out/(1E3*a.VBR_mean_bitrate_kbps));a.bWriteVbrTag||(a.findReplayGain=!1,a.decode_on_the_fly=!1,b.findPeakSample=!1);b.findReplayGain=a.findReplayGain;b.decode_on_the_fly=a.decode_on_the_fly;b.decode_on_the_fly&&(b.findPeakSample=!0);if(b.findReplayGain&&
k.InitGainAnalysis(b.rgdata,a.out_samplerate)==U.INIT_GAIN_ANALYSIS_ERROR)return a.internal_flags=null,-6;b.decode_on_the_fly&&!a.decode_only&&(null!=b.hip&&j.hip_decode_exit(b.hip),b.hip=j.hip_decode_init());b.mode_gr=24E3>=a.out_samplerate?1:2;a.framesize=576*b.mode_gr;a.encoder_delay=e.ENCDELAY;b.resample_ratio=a.in_samplerate/a.out_samplerate;switch(a.VBR){case F.vbr_mt:case F.vbr_rh:case F.vbr_mtrh:a.compression_ratio=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5][a.VBR_q];break;case F.vbr_abr:a.compression_ratio=
16*a.out_samplerate*b.channels_out/(1E3*a.VBR_mean_bitrate_kbps);break;default:a.compression_ratio=16*a.out_samplerate*b.channels_out/(1E3*a.brate)}a.mode==ia.NOT_SET&&(a.mode=ia.JOINT_STEREO);0<a.highpassfreq?(b.highpass1=2*a.highpassfreq,b.highpass2=0<=a.highpasswidth?2*(a.highpassfreq+a.highpasswidth):2*a.highpassfreq,b.highpass1/=a.out_samplerate,b.highpass2/=a.out_samplerate):(b.highpass1=0,b.highpass2=0);0<a.lowpassfreq?(b.lowpass2=2*a.lowpassfreq,0<=a.lowpasswidth?(b.lowpass1=2*(a.lowpassfreq-
a.lowpasswidth),0>b.lowpass1&&(b.lowpass1=0)):b.lowpass1=2*a.lowpassfreq,b.lowpass1/=a.out_samplerate,b.lowpass2/=a.out_samplerate):(b.lowpass1=0,b.lowpass2=0);var f=a.internal_flags,n=32,E=-1;if(0<f.lowpass1){for(var C=999,h=0;31>=h;h++)l=h/31,l>=f.lowpass2&&(n=Math.min(n,h)),f.lowpass1<l&&l<f.lowpass2&&(C=Math.min(C,h));f.lowpass1=999==C?(n-0.75)/31:(C-0.75)/31;f.lowpass2=n/31}0<f.highpass2&&f.highpass2<0.9*(0.75/31)&&(f.highpass1=0,f.highpass2=0,N.err.println("Warning: highpass filter disabled.  highpass frequency too small\n"));
if(0<f.highpass2){n=-1;for(h=0;31>=h;h++)l=h/31,l<=f.highpass1&&(E=Math.max(E,h)),f.highpass1<l&&l<f.highpass2&&(n=Math.max(n,h));f.highpass1=E/31;f.highpass2=-1==n?(E+0.75)/31:(n+0.75)/31}for(h=0;32>h;h++)l=h/31,E=f.highpass2>f.highpass1?1<(f.highpass2-l)/(f.highpass2-f.highpass1+1E-20)?0:0>=(f.highpass2-l)/(f.highpass2-f.highpass1+1E-20)?1:Math.cos(Math.PI/2*((f.highpass2-l)/(f.highpass2-f.highpass1+1E-20))):1,l=f.lowpass2>f.lowpass1?1<(l-f.lowpass1)/(f.lowpass2-f.lowpass1+1E-20)?0:0>=(l-f.lowpass1)/
(f.lowpass2-f.lowpass1+1E-20)?1:Math.cos(Math.PI/2*((l-f.lowpass1)/(f.lowpass2-f.lowpass1+1E-20))):1,f.amp_filter[h]=E*l;b.samplerate_index=z(a.out_samplerate,a);if(0>b.samplerate_index)return a.internal_flags=null,-1;if(a.VBR==F.vbr_off)if(a.free_format)b.bitrate_index=0;else{if(a.brate=x(a.brate,a.version,a.out_samplerate),b.bitrate_index=u(a.brate,a.version,a.out_samplerate),0>=b.bitrate_index)return a.internal_flags=null,-1}else b.bitrate_index=1;a.analysis&&(a.bWriteVbrTag=!1);null!=b.pinfo&&
(a.bWriteVbrTag=!1);g.init_bit_stream_w(b);f=b.samplerate_index+3*a.version+6*(16E3>a.out_samplerate?1:0);for(h=0;h<e.SBMAX_l+1;h++)b.scalefac_band.l[h]=c.sfBandIndex[f].l[h];for(h=0;h<e.PSFB21+1;h++)l=(b.scalefac_band.l[22]-b.scalefac_band.l[21])/e.PSFB21,l=b.scalefac_band.l[21]+h*l,b.scalefac_band.psfb21[h]=l;b.scalefac_band.psfb21[e.PSFB21]=576;for(h=0;h<e.SBMAX_s+1;h++)b.scalefac_band.s[h]=c.sfBandIndex[f].s[h];for(h=0;h<e.PSFB12+1;h++)l=(b.scalefac_band.s[13]-b.scalefac_band.s[12])/e.PSFB12,
l=b.scalefac_band.s[12]+h*l,b.scalefac_band.psfb12[h]=l;b.scalefac_band.psfb12[e.PSFB12]=192;b.sideinfo_len=1==a.version?1==b.channels_out?21:36:1==b.channels_out?13:21;a.error_protection&&(b.sideinfo_len+=2);f=a.internal_flags;a.frameNum=0;a.write_id3tag_automatic&&s.id3tag_write_v2(a);f.bitrate_stereoMode_Hist=va([16,5]);f.bitrate_blockType_Hist=va([16,6]);f.PeakSample=0;a.bWriteVbrTag&&r.InitVbrTag(a);b.Class_ID=i;for(f=0;19>f;f++)b.nsPsy.pefirbuf[f]=700*b.mode_gr*b.channels_out;-1==a.ATHtype&&
(a.ATHtype=4);switch(a.VBR){case F.vbr_mt:a.VBR=F.vbr_mtrh;case F.vbr_mtrh:null==a.useTemporal&&(a.useTemporal=!1);d.apply_preset(a,500-10*a.VBR_q,0);0>a.quality&&(a.quality=LAME_DEFAULT_QUALITY);5>a.quality&&(a.quality=0);5<a.quality&&(a.quality=5);b.PSY.mask_adjust=a.maskingadjust;b.PSY.mask_adjust_short=a.maskingadjust_short;b.sfb21_extra=a.experimentalY?!1:44E3<a.out_samplerate;b.iteration_loop=new VBRNewIterationLoop(B);break;case F.vbr_rh:d.apply_preset(a,500-10*a.VBR_q,0);b.PSY.mask_adjust=
a.maskingadjust;b.PSY.mask_adjust_short=a.maskingadjust_short;b.sfb21_extra=a.experimentalY?!1:44E3<a.out_samplerate;6<a.quality&&(a.quality=6);0>a.quality&&(a.quality=LAME_DEFAULT_QUALITY);b.iteration_loop=new VBROldIterationLoop(B);break;default:b.sfb21_extra=!1,0>a.quality&&(a.quality=LAME_DEFAULT_QUALITY),f=a.VBR,f==F.vbr_off&&(a.VBR_mean_bitrate_kbps=a.brate),d.apply_preset(a,a.VBR_mean_bitrate_kbps,0),a.VBR=f,b.PSY.mask_adjust=a.maskingadjust,b.PSY.mask_adjust_short=a.maskingadjust_short,b.iteration_loop=
f==F.vbr_off?new Hc(B):new ABRIterationLoop(B)}if(a.VBR!=F.vbr_off){b.VBR_min_bitrate=1;b.VBR_max_bitrate=14;16E3>a.out_samplerate&&(b.VBR_max_bitrate=8);if(0!=a.VBR_min_bitrate_kbps&&(a.VBR_min_bitrate_kbps=x(a.VBR_min_bitrate_kbps,a.version,a.out_samplerate),b.VBR_min_bitrate=u(a.VBR_min_bitrate_kbps,a.version,a.out_samplerate),0>b.VBR_min_bitrate)||0!=a.VBR_max_bitrate_kbps&&(a.VBR_max_bitrate_kbps=x(a.VBR_max_bitrate_kbps,a.version,a.out_samplerate),b.VBR_max_bitrate=u(a.VBR_max_bitrate_kbps,
a.version,a.out_samplerate),0>b.VBR_max_bitrate))return-1;a.VBR_min_bitrate_kbps=v.bitrate_table[a.version][b.VBR_min_bitrate];a.VBR_max_bitrate_kbps=v.bitrate_table[a.version][b.VBR_max_bitrate];a.VBR_mean_bitrate_kbps=Math.min(v.bitrate_table[a.version][b.VBR_max_bitrate],a.VBR_mean_bitrate_kbps);a.VBR_mean_bitrate_kbps=Math.max(v.bitrate_table[a.version][b.VBR_min_bitrate],a.VBR_mean_bitrate_kbps)}a.tune&&(b.PSY.mask_adjust+=a.tune_value_a,b.PSY.mask_adjust_short+=a.tune_value_a);f=a.internal_flags;
switch(a.quality){default:case 9:f.psymodel=0;f.noise_shaping=0;f.noise_shaping_amp=0;f.noise_shaping_stop=0;f.use_best_huffman=0;f.full_outer_loop=0;break;case 8:a.quality=7;case 7:f.psymodel=1;f.noise_shaping=0;f.noise_shaping_amp=0;f.noise_shaping_stop=0;f.use_best_huffman=0;f.full_outer_loop=0;break;case 6:f.psymodel=1;0==f.noise_shaping&&(f.noise_shaping=1);f.noise_shaping_amp=0;f.noise_shaping_stop=0;-1==f.subblock_gain&&(f.subblock_gain=1);f.use_best_huffman=0;f.full_outer_loop=0;break;case 5:f.psymodel=
1;0==f.noise_shaping&&(f.noise_shaping=1);f.noise_shaping_amp=0;f.noise_shaping_stop=0;-1==f.subblock_gain&&(f.subblock_gain=1);f.use_best_huffman=0;f.full_outer_loop=0;break;case 4:f.psymodel=1;0==f.noise_shaping&&(f.noise_shaping=1);f.noise_shaping_amp=0;f.noise_shaping_stop=0;-1==f.subblock_gain&&(f.subblock_gain=1);f.use_best_huffman=1;f.full_outer_loop=0;break;case 3:f.psymodel=1;0==f.noise_shaping&&(f.noise_shaping=1);f.noise_shaping_amp=1;f.noise_shaping_stop=1;-1==f.subblock_gain&&(f.subblock_gain=
1);f.use_best_huffman=1;f.full_outer_loop=0;break;case 2:f.psymodel=1;0==f.noise_shaping&&(f.noise_shaping=1);0==f.substep_shaping&&(f.substep_shaping=2);f.noise_shaping_amp=1;f.noise_shaping_stop=1;-1==f.subblock_gain&&(f.subblock_gain=1);f.use_best_huffman=1;f.full_outer_loop=0;break;case 1:f.psymodel=1;0==f.noise_shaping&&(f.noise_shaping=1);0==f.substep_shaping&&(f.substep_shaping=2);f.noise_shaping_amp=2;f.noise_shaping_stop=1;-1==f.subblock_gain&&(f.subblock_gain=1);f.use_best_huffman=1;f.full_outer_loop=
0;break;case 0:f.psymodel=1,0==f.noise_shaping&&(f.noise_shaping=1),0==f.substep_shaping&&(f.substep_shaping=2),f.noise_shaping_amp=2,f.noise_shaping_stop=1,-1==f.subblock_gain&&(f.subblock_gain=1),f.use_best_huffman=1,f.full_outer_loop=0}b.ATH.useAdjust=0>a.athaa_type?3:a.athaa_type;b.ATH.aaSensitivityP=Math.pow(10,a.athaa_sensitivity/-10);null==a.short_blocks&&(a.short_blocks=ya.short_block_allowed);if(a.short_blocks==ya.short_block_allowed&&(a.mode==ia.JOINT_STEREO||a.mode==ia.STEREO))a.short_blocks=
ya.short_block_coupled;0>a.quant_comp&&(a.quant_comp=1);0>a.quant_comp_short&&(a.quant_comp_short=0);0>a.msfix&&(a.msfix=0);a.exp_nspsytune|=1;0>a.internal_flags.nsPsy.attackthre&&(a.internal_flags.nsPsy.attackthre=Sb.NSATTACKTHRE);0>a.internal_flags.nsPsy.attackthre_s&&(a.internal_flags.nsPsy.attackthre_s=Sb.NSATTACKTHRE_S);0>a.scale&&(a.scale=1);0>a.ATHtype&&(a.ATHtype=4);0>a.ATHcurve&&(a.ATHcurve=4);0>a.athaa_loudapprox&&(a.athaa_loudapprox=2);0>a.interChRatio&&(a.interChRatio=0);null==a.useTemporal&&
(a.useTemporal=!0);b.slot_lag=b.frac_SpF=0;a.VBR==F.vbr_off&&(b.slot_lag=b.frac_SpF=72E3*(a.version+1)*a.brate%a.out_samplerate|0);c.iteration_init(a);y.psymodel_init(a);return 0};this.lame_encode_flush=function(a,b,c,d){var f=a.internal_flags,i=nc([2,1152]),j=0,k,l,n=f.mf_samples_to_encode-e.POSTDELAY,p=h(a);if(1>f.mf_samples_to_encode)return 0;k=0;a.in_samplerate!=a.out_samplerate&&(n+=16*a.out_samplerate/a.in_samplerate);l=a.framesize-n%a.framesize;576>l&&(l+=a.framesize);a.encoder_padding=l;for(l=
(n+l)/a.framesize;0<l&&0<=j;){var q=p-f.mf_size,n=a.frameNum,q=q*a.in_samplerate,q=q/a.out_samplerate;1152<q&&(q=1152);1>q&&(q=1);j=d-k;0==d&&(j=0);j=this.lame_encode_buffer(a,i[0],i[1],q,b,c,j);c+=j;k+=j;l-=n!=a.frameNum?1:0}f.mf_samples_to_encode=0;if(0>j)return j;j=d-k;0==d&&(j=0);g.flush_bitstream(a);j=g.copy_buffer(f,b,c,j,1);if(0>j)return j;c+=j;k+=j;j=d-k;0==d&&(j=0);if(a.write_id3tag_automatic){s.id3tag_write_v1(a);j=g.copy_buffer(f,b,c,j,0);if(0>j)return j;k+=j}return k};this.lame_encode_buffer=
function(a,c,d,f,e,g,h){var j=a.internal_flags,k=[null,null];if(j.Class_ID!=i)return-3;if(0==f)return 0;if(null==j.in_buffer_0||j.in_buffer_nsamples<f)j.in_buffer_0=K(f),j.in_buffer_1=K(f),j.in_buffer_nsamples=f;k[0]=j.in_buffer_0;k[1]=j.in_buffer_1;for(var l=0;l<f;l++)k[0][l]=c[l],1<j.channels_in&&(k[1][l]=d[l]);return b(a,k[0],k[1],f,e,g,h)}}function Pc(){this.setModules=function(){}}function Qc(){this.setModules=function(){}}function Rc(){}function Sc(){this.setModules=function(){}}function xa(){this.sampleRate=
this.channels=this.dataLen=this.dataOffset=0}function Rb(e){return e.charCodeAt(0)<<24|e.charCodeAt(1)<<16|e.charCodeAt(2)<<8|e.charCodeAt(3)}var Ba={fill:function(e,p,l,v){if(2==arguments.length)for(var x=0;x<e.length;x++)e[x]=arguments[1];else for(x=p;x<l;x++)e[x]=v}},N={arraycopy:function(e,p,l,v,x){for(x=p+x;p<x;)l[v++]=e[p++]}},X={SQRT2:1.4142135623730951,FAST_LOG10:function(e){return Math.log10(e)},FAST_LOG10_X:function(e,p){return Math.log10(e)*p}};ya.short_block_allowed=new ya(0);ya.short_block_coupled=
new ya(1);ya.short_block_dispensed=new ya(2);ya.short_block_forced=new ya(3);var Ma={MAX_VALUE:3.4028235E38};F.vbr_off=new F(0);F.vbr_mt=new F(1);F.vbr_rh=new F(2);F.vbr_abr=new F(3);F.vbr_mtrh=new F(4);F.vbr_default=F.vbr_mtrh;ia.STEREO=new ia(0);ia.JOINT_STEREO=new ia(1);ia.DUAL_CHANNEL=new ia(2);ia.MONO=new ia(3);ia.NOT_SET=new ia(4);U.STEPS_per_dB=100;U.MAX_dB=120;U.GAIN_NOT_ENOUGH_SAMPLES=-24601;U.GAIN_ANALYSIS_ERROR=0;U.GAIN_ANALYSIS_OK=1;U.INIT_GAIN_ANALYSIS_ERROR=0;U.INIT_GAIN_ANALYSIS_OK=
1;U.YULE_ORDER=10;U.MAX_ORDER=U.YULE_ORDER;U.MAX_SAMP_FREQ=48E3;U.RMS_WINDOW_TIME_NUMERATOR=1;U.RMS_WINDOW_TIME_DENOMINATOR=20;U.MAX_SAMPLES_PER_WINDOW=U.MAX_SAMP_FREQ*U.RMS_WINDOW_TIME_NUMERATOR/U.RMS_WINDOW_TIME_DENOMINATOR+1;ka.EQ=function(e,p){return Math.abs(e)>Math.abs(p)?Math.abs(e-p)<=1E-6*Math.abs(e):Math.abs(e-p)<=1E-6*Math.abs(p)};ka.NEQ=function(e,p){return!ka.EQ(e,p)};Da.NUMTOCENTRIES=100;Da.MAXFRAMESIZE=2880;var v={t1HB:[1,1,1,0],t2HB:[1,2,1,3,1,1,3,2,0],t3HB:[3,2,1,1,1,1,3,2,0],t5HB:[1,
2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],t6HB:[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],t7HB:[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],t8HB:[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,10,4,13,5,8,11,5,1,12,4,4,1,1,0],t9HB:[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],t10HB:[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,
7,8,4,4,2,0],t11HB:[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],t12HB:[9,6,16,33,41,39,38,26,7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],t13HB:[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,
40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,
64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],t15HB:[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,
73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],t16HB:[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,
207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,
393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],t24HB:[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,
541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,
10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],t32HB:[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],t33HB:[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],t1l:[1,4,3,5],t2l:[1,4,7,4,5,7,6,7,8],t3l:[2,3,7,4,4,7,6,7,8],t5l:[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],
t6l:[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],t7l:[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],t8l:[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,13,13],t9l:[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],t10l:[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,
13],t11l:[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],t12l:[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],t13l:[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,
12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,
18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],t15l:[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,
14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],t16_5l:[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,
12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,
17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],t16l:[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,
14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],t24l:[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,
9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,
13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],t32l:[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],t33l:[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8]};v.ht=[new W(0,0,null,null),new W(2,0,v.t1HB,v.t1l),new W(3,0,v.t2HB,v.t2l),new W(3,0,v.t3HB,v.t3l),new W(0,0,null,null),new W(4,0,v.t5HB,v.t5l),new W(4,0,v.t6HB,v.t6l),new W(6,0,v.t7HB,v.t7l),new W(6,0,v.t8HB,v.t8l),new W(6,0,v.t9HB,v.t9l),new W(8,0,v.t10HB,v.t10l),new W(8,0,v.t11HB,v.t11l),new W(8,0,v.t12HB,v.t12l),new W(16,
0,v.t13HB,v.t13l),new W(0,0,null,v.t16_5l),new W(16,0,v.t15HB,v.t15l),new W(1,1,v.t16HB,v.t16l),new W(2,3,v.t16HB,v.t16l),new W(3,7,v.t16HB,v.t16l),new W(4,15,v.t16HB,v.t16l),new W(6,63,v.t16HB,v.t16l),new W(8,255,v.t16HB,v.t16l),new W(10,1023,v.t16HB,v.t16l),new W(13,8191,v.t16HB,v.t16l),new W(4,15,v.t24HB,v.t24l),new W(5,31,v.t24HB,v.t24l),new W(6,63,v.t24HB,v.t24l),new W(7,127,v.t24HB,v.t24l),new W(8,255,v.t24HB,v.t24l),new W(9,511,v.t24HB,v.t24l),new W(11,2047,v.t24HB,v.t24l),new W(13,8191,v.t24HB,
v.t24l),new W(0,0,v.t32HB,v.t32l),new W(0,0,v.t33HB,v.t33l)];v.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,
983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,
851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,
1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366];v.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296];v.table56=[65539,262148,
458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369];v.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]];v.samplerate_table=[[22050,24E3,16E3,-1],[44100,48E3,32E3,-1],[11025,12E3,8E3,-1]];v.scfsi_band=[0,6,11,16,21];ca.Q_MAX=257;ca.Q_MAX2=116;ca.LARGE_BITS=1E5;ca.IXMAX_VAL=8206;var ra={};ra.SFBMAX=3*e.SBMAX_s;e.ENCDELAY=576;e.POSTDELAY=
1152;e.MDCTDELAY=48;e.FFTOFFSET=224+e.MDCTDELAY;e.DECDELAY=528;e.SBLIMIT=32;e.CBANDS=64;e.SBPSY_l=21;e.SBPSY_s=12;e.SBMAX_l=22;e.SBMAX_s=13;e.PSFB21=6;e.PSFB12=6;e.BLKSIZE=1024;e.HBLKSIZE=e.BLKSIZE/2+1;e.BLKSIZE_s=256;e.HBLKSIZE_s=e.BLKSIZE_s/2+1;e.NORM_TYPE=0;e.START_TYPE=1;e.SHORT_TYPE=2;e.STOP_TYPE=3;e.MPG_MD_LR_LR=0;e.MPG_MD_LR_I=1;e.MPG_MD_MS_LR=2;e.MPG_MD_MS_I=3;e.fircoef=[-0.1039435,-0.1892065,-0.0432472*5,-0.155915,3.898045E-17,0.0467745*5,0.50455,0.756825,0.187098*5];aa.MFSIZE=3456+e.ENCDELAY-
e.MDCTDELAY;aa.MAX_HEADER_BUF=256;aa.MAX_BITS_PER_CHANNEL=4095;aa.MAX_BITS_PER_GRANULE=7680;aa.BPC=320;xa.RIFF=Rb("RIFF");xa.WAVE=Rb("WAVE");xa.fmt_=Rb("fmt ");xa.data=Rb("data");xa.readHeader=function(e){var p=new xa,l=e.getUint32(0,!1);if(xa.RIFF==l&&(e.getUint32(4,!0),xa.WAVE==e.getUint32(8,!1)&&xa.fmt_==e.getUint32(12,!1))){var v=e.getUint32(16,!0),x=20;switch(v){case 16:case 18:p.channels=e.getUint16(x+2,!0);p.sampleRate=e.getUint32(x+4,!0);break;default:throw"extended fmt chunk not implemented";
}for(var x=x+v,v=xa.data,u=0;v!=l;){l=e.getUint32(x,!1);u=e.getUint32(x+4,!0);if(v==l)break;x+=u+8}p.dataLen=u;p.dataOffset=x+8;return p}};ra.SFBMAX=3*e.SBMAX_s;lamejs.Mp3Encoder=function(e,p,l){3!=arguments.length&&(console.error("WARN: Mp3Encoder(channels, samplerate, kbps) not specified"),e=1,p=44100,l=128);var v=new E,x=new Pc,u=new U,q=new ka,h=new Bc,b=new ca,n=new Jc,a=new Da,f=new Ac,F=new Sc,k=new Cc,g=new La,d=new Qc,c=new Rc;v.setModules(u,q,h,b,n,a,f,F,c);q.setModules(u,c,f,a);F.setModules(q,
f);h.setModules(v);n.setModules(q,k,b,g);b.setModules(g,k,v.enc.psy);k.setModules(q);g.setModules(b);a.setModules(v,q,f);x.setModules(d,c);d.setModules(f,F,h);var B=v.lame_init();B.num_channels=e;B.in_samplerate=p;B.brate=l;B.mode=ia.STEREO;B.quality=3;B.bWriteVbrTag=!1;B.disable_reservoir=!0;B.write_id3tag_automatic=!1;v.lame_init_params(B);var y=1152,r=0|1.25*y+7200,s=new Int8Array(r);this.encodeBuffer=function(a,b){1==e&&(b=a);a.length>y&&(y=a.length,r=0|1.25*y+7200,s=new Int8Array(r));var c=v.lame_encode_buffer(B,
a,b,a.length,s,0,r);return new Int8Array(s.subarray(0,c))};this.flush=function(){var a=v.lame_encode_flush(B,s,0,r);return new Int8Array(s.subarray(0,a))}};lamejs.WavHeader=xa}lamejs();
