$(function () {
    let lastNote = null

    //alt + mouse note handle
    $(MPP.piano.rootElement).mousemove((e) => {
        if (!e.altKey) return
        e.preventDefault()
        var pos = translateMouseEvent(e)
        var hit = MPP.piano.renderer.getHit(pos.x, pos.y)
        if (hit && lastNote != hit.key.note) {
            MPP.press(hit.key.note, hit.v)
            MPP.release(hit.key.note)
            lastNote = hit.key.note
        }
    })

    function translateMouseEvent(evt) {
        var element = evt.target
        var offx = 0
        var offy = 0
        do {
            if (!element) break // wtf, wtf?
            offx += element.offsetLeft
            offy += element.offsetTop
        } while ((element = element.offsetParent))
        return {
            x: (evt.pageX - offx) * window.devicePixelRatio,
            y: (evt.pageY - offy) * window.devicePixelRatio,
        }
    }

    //audio
    MPP.piano.audio.lramp = 0
    MPP.piano.audio.sstop = 0

    //addon ced
    MPP.ced_addon = {}

    MPP.ced_addon.debug = {
        startTime: null,
        endTime: null,
        fps: 0,
        frame: 0,

        NPSTimer: null,
        notes: 0,
        notesDir: {
            ch1: 0,
            ch2: 0,
            ch3: 0,
            ch4: 0,
            ch5: 0,
            ch6: 0,
            ch7: 0,
            ch8: 0,
            ch9: 0,
            ch10: 0,
            ch11: 0,
            ch12: 0,
            ch13: 0,
            ch14: 0,
            ch15: 0,
            ch16: 0,
            ch17: 0,
        },
        nps: 0,
        fps: 0,

        notesThisFrame: 0,
        npsHistory: [],

        maxNPS: 0,
        maxPolyphony: 0,
    }

    MPP.ced_addon.functions = {
        initFPS() {
            MPP.ced_addon.debug.startTime = new Date().getTime()

            function gameLoop() {
                MPP.ced_addon.debug.frame++
                document.getElementById(
                    "fps"
                ).innerText = `FPS: ${MPP.ced_addon.debug.fps}`
                MPP.ced_addon.debug.endTime = new Date().getTime()
                if (
                    MPP.ced_addon.debug.endTime -
                        MPP.ced_addon.debug.startTime >=
                    1000
                ) {
                    MPP.ced_addon.debug.fps = MPP.ced_addon.debug.frame
                    MPP.ced_addon.debug.frame = 0
                    MPP.ced_addon.debug.startTime = new Date().getTime()
                }
                //MPP.ced_addon.debug.notesThisFrame = 0
                MPP.ced_addon.debug.npsHistory.push({
                    notesThisFrame: MPP.ced_addon.debug.notesThisFrame,
                    timestamp: +new Date(),
                })
                const newDate = +new Date() - 1000
                MPP.ced_addon.debug.nps = 0
                MPP.ced_addon.debug.npsHistory =
                    MPP.ced_addon.debug.npsHistory.filter(
                        (entry) => entry.timestamp >= newDate
                    )
                for (const entry of MPP.ced_addon.debug.npsHistory) {
                    MPP.ced_addon.debug.nps += entry.notesThisFrame
                }

                document.getElementById(
                    "nps"
                ).innerHTML = `NPS: ${MPP.ced_addon.debug.nps} (ntf: ${MPP.ced_addon.debug.notesThisFrame})`

                requestAnimationFrame(gameLoop)
                MPP.ced_addon.debug.notesThisFrame = 0
            }
            gameLoop()
        },
        addDebugCounters() {
            var nc = (MPP.ced_addon.debug.notes += 1)
            var ret = nc.toString().padStart(6, "0")
            document.getElementById("notes").innerText = `Notes: ${ret}`
            MPP.ced_addon.debug.notesThisFrame++
            //document.getElementById('nps').innerHTML = `NPS: ${MPP.ced_addon.debug.nps}`;
        },
        addDebugCountersWithChannels(ch) {
            var nc = (MPP.ced_addon.debug.notesDir[`ch${ch + 1}`] += 1)
            var ret = nc.toString().padStart(6, "0")
            if (ch === 16) {
                document.getElementById(
                    `notes-${ch + 1}`
                ).innerText = `Other: ${ret}`
            } else {
                document.getElementById(`notes-${ch + 1}`).innerText = `Ch${
                    ch + 1
                }: ${ret}`
            }
        },
        initNPS() {
            MPP.ced_addon.debug.NPSTimer = setInterval(() => {
                //document.getElementById('nps').innerHTML = `NPS: ${MPP.ced_addon.debug.nps}`;
                //MPP.ced_addon.debug.nps = 0;
                //console.log(MPP.ced_addon.debug.startTime)
            }, 1000)
        },
    }

    MPP.ced_addon.functions.initFPS()
    MPP.ced_addon.functions.initNPS()

    setInterval(() => {
        const arr = Object.keys(MPP.piano.keys).map(
            (a) => MPP.piano.keys[a].blips.length
        )
        const reducer = (accumulator, curr) => accumulator + curr
        const polyphony = arr.reduce(reducer) || 0

        MPP.ced_addon.debug.maxPolyphony = Math.max(
            polyphony,
            MPP.ced_addon.debug.maxPolyphony
        )

        document.getElementById(
            "polyphony"
        ).innerText = `Polyphony: ${polyphony} / ${MPP.ced_addon.debug.maxPolyphony}`

        const polyphonyBar = document.getElementById("polyphonyBar")
        polyphonyBar.max = MPP.ced_addon.debug.maxPolyphony
        polyphonyBar.value = polyphony
    }, 20)
})
