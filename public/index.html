<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
		<title>Multiplayer Piano</title>
		<meta name="description" content="An online piano you can play alone or with others in real-time. MIDI support, 88 keys, velocity sensitive.  You can show off your skill or chat while listening to others play."/>
		<link rel="stylesheet" href="css/screen.css"/>
		<link rel="icon" href="img/favicon.png" />
	</head>
	<body>
		<p style="z-index: 401; position: fixed; bottom: 70px; right: 6px; font-size: 12px; color: red;">Please, do not cuss me or anyone else, and do not troll etc.</p>
		<div id="chat">
			<ul></ul>
			<input placeholder="You can chat with this thing." class="translate" maxlength="512"/>
		</div>

		<div id="names"></div>
		<div id="piano"></div>
		<div id="cursors"></div>
		<div id="debug" style="display: none;">
			<h2>[DEBUG]</h2>
			<h3>Counters</h3>
			<output id="notes">Notes: 000000</output>
			<br>
            <div style="max-width: 200px; display: flex; align-items: center; flex-wrap: wrap; justify-content: space-between;">
                <output id="notes-1" style="font-size: 8px;">Ch1: 000000</output>
                <br>
                <output id="notes-2" style="font-size: 8px;">Ch2: 000000</output>
                <br>
                <output id="notes-3" style="font-size: 8px;">Ch3: 000000</output>
                <br>
                <output id="notes-4" style="font-size: 8px;">Ch4: 000000</output>
                <br>
                <output id="notes-5" style="font-size: 8px;">Ch5: 000000</output>
                <br>
                <output id="notes-6" style="font-size: 8px;">Ch6: 000000</output>
                <br>
                <output id="notes-7" style="font-size: 8px;">Ch7: 000000</output>
                <br>
                <output id="notes-8" style="font-size: 8px;">Ch8: 000000</output>
                <br>
                <output id="notes-9" style="font-size: 8px;">Ch9: 000000</output>
                <br>
                <output id="notes-10" style="font-size: 8px;">Ch10: 000000</output>
                <br>
                <output id="notes-11" style="font-size: 8px;">Ch11: 000000</output>
                <br>
                <output id="notes-12" style="font-size: 8px;">Ch12: 000000</output>
                <br>
                <output id="notes-13" style="font-size: 8px;">Ch13: 000000</output>
                <br>
                <output id="notes-14" style="font-size: 8px;">Ch14: 000000</output>
                <br>
                <output id="notes-15" style="font-size: 8px;">Ch15: 000000</output>
                <br>
                <output id="notes-16" style="font-size: 8px;">Ch16: 000000</output>
                <br>
                <output id="notes-17" style="font-size: 8px;">Other: 000000</output>
            </div>
			<output id="nps">NPS: 0</output>
			<br>
			<output id="fps">FPS: 0</output>
			<br>
			<output id="polyphony">Polyphony: 0</output><br>
            <progress id="polyphonyBar" value="0" max="0"></progress>
			<br>
			<output id="noteQuota">Quota: 0/0</output>
			<h3>PianoRoll</h3>
			<canvas id="pianoroll"></canvas>
		</div>

		<noscript>
			<center>
				<p>
					Multiplayer Piano is an online, full 88-key piano you can play alone or with others in real-time.  Plug up your MIDI keyboard, MIDI in and out are supported.  You should be able to hear some seriously talented piano players performing here!  Join in or just chat and listen.
				</p>
				<p>
					For good performance, Chrome is highly recommended.  Firefox also supports the requisite Web Audio API, but performance may not be as good.  Chrome has Web MIDI.
				</p>
				<p>
					Of course, first you need to <a href="http://www.enable-javascript.com/" class="link">Enable Javascript</a> or it won't do anything...!
				</p>
			</center>
		</noscript>

		<div id="bottom">
			<div class="relative">
				<div id="room">
					<div class="info"></div>
					<div class="expand"></div>
					<div class="more">
						<div class="new translate">New Room...</div>
					</div>
				</div>
				<div id="buttons">
					<div id="new-room-btn" class="ugly-button translate">New Room...</div>
					<div id="play-alone-btn" class="ugly-button">Play Alone</div>
					<div id="room-settings-btn" class="ugly-button">Room Settings</div>
					<div id="midi-btn" class="ugly-button translate">MIDI In/Out</div>
					<div id="none-btn" class="ugly-button translate">None</div>
					<div id="synth-btn" class="ugly-button translate">Synth</div>
					<div id="sound-btn" class="ugly-button sound-btn">Sound Select</div>
				</div>
				
				<div id="status"></div>
				<div id="volume"></div>
				<div id="volume-label">volume <out id="volpercent">60%</out></div>
				<div id="quota">
					<div class="value"></div>
				</div>
			</div>
		</div>

		<div id="modal">
			<div class="bg"></div>
			<div id="modals">
				<div id="new-room" class="dialog">
					<p><input type="text" name="name" placeholder="room name" class="text translate" maxlength="512"/></p>
					<p><label><input type="checkbox" name="visible" class="checkbox translate" checked>Visible (open to everyone)</label></p>
					</label></p>
					<button class="submit">go</button>
				</div>
				<div id="room-settings" class="dialog">
					<p><div class="ugly-button drop-crown">Drop crown</div></p>
					<p><label><input type="checkbox" name="visible" class="checkbox translate" checked>Visible (open to everyone)</label></p>
					</label></p>
					<p><label><input type="checkbox" name="chat" class="checkbox translate" checked>Enable Chat</label></p>
					<p><label><input type="checkbox" name="crownsolo" class="checkbox">Only Owner can Play<button class="submit">APPLY</button>
					<p><label>Inner color: &nbsp;<input type="color" name="color" placeholder="" maxlength="7" class="color"></label></p>
					<p><label>Outer color: &nbsp;<input type="color" name="color2" placeholder="" maxlength="7" class="color2"></label></p>
				</div>
				<div id="rename" class="dialog">
					<p><input type="text" name="name" placeholder="My Fancy New Name" maxlength="40" class="text"/></p>
					<p><input type="color" name="color" placeholder="" maxlength="7" class="color"/></p>
                    <div class="userinfo">
                        <p>User Info</p>
                        <div class="display: flex;">
                            <p id="username">name: </p>
                            <p id="_id">_id: </p>
                            <p id="id">id: </p>
                            <p id="color">color: </p>
                        </div>
                    </div>
					<button class="submit">USER SET</button>
				</div>
                <div id="change-color" class="dialog" style="display: flex; flex-wrap: wrap;">
					<p>Ch.1 <input type="color" name="ch-color-1" id="ch-color-1" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.2 <input type="color" name="ch-color-2" id="ch-color-2" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.3 <input type="color" name="ch-color-3" id="ch-color-3" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.4 <input type="color" name="ch-color-4" id="ch-color-4" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.5 <input type="color" name="ch-color-5" id="ch-color-5" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.6 <input type="color" name="ch-color-6" id="ch-color-6" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.7 <input type="color" name="ch-color-7" id="ch-color-7" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.8 <input type="color" name="ch-color-8" id="ch-color-8" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.9 <input type="color" name="ch-color-9" id="ch-color-9" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.10 <input type="color" name="ch-color-10" id="ch-color-10" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.11 <input type="color" name="ch-color-11" id="ch-color-11" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.12 <input type="color" name="ch-color-12" id="ch-color-12" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.13 <input type="color" name="ch-color-13" id="ch-color-13" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.14 <input type="color" name="ch-color-14" id="ch-color-14" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.15 <input type="color" name="ch-color-15" id="ch-color-15" placeholder="" maxlength="7" class="color"/></p>
                    <p>Ch.16 <input type="color" name="ch-color-16" id="ch-color-16" placeholder="" maxlength="7" class="color"/></p>
                    <button class="random-color">Random</button>
					<button class="submit">COLOR SET</button>
				</div>
			</div>
		</div>

		<script src="js/jquery.min.js"></script>
		<script src="js/util.js"></script>
		<script src="js/Color.js"></script>
		<script src="js/Client.js"></script>
		<script src="js/NoteQuota.js"></script>
		<script src="js/lame.min.js"></script>
		<script src="js/script.js"></script>
		<script src="js/drawing.js"></script>
		<!--<script src="js/extbinary.js"></script>-->
		<script src="js/curssettings.js"></script>
		<script src="js/addon.js"></script>
		<script src="js/ced.addon.js"></script>
		<script src="js/WOPPPallete.js"></script>
		<script src="js/PianoRoll.js"></script>
	</body>
</html>
