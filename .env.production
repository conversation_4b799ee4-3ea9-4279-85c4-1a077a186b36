# Production environment configuration for optimal performance

# Set Node.js environment to production
NODE_ENV=production

# Logging configuration - only log errors in production
LOG_LEVEL=error

# Session configuration
SESSION_SECRET=your-production-session-secret-here

# Fake user password (if needed)
FAKE_USER_PASSWORD=your-fake-user-password-here

# Performance optimizations
# Increase memory limit and enable garbage collection monitoring
NODE_OPTIONS="--max-old-space-size=4096 --expose-gc --trace-gc"

# Additional Node.js performance flags for high-load scenarios
# Uncomment for extreme optimization:
# NODE_OPTIONS="--max-old-space-size=4096 --expose-gc --trace-gc --optimize-for-size --max-semi-space-size=128"

# Memory monitoring settings
MEMORY_MONITOR_ENABLED=true
HIGH_LOAD_THRESHOLD=150
MONITOR_INTERVAL=60000
