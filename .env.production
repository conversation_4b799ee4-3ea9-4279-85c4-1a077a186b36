# Production environment configuration for optimal performance

# Set Node.js environment to production
NODE_ENV=production

# Logging configuration - only log errors in production
LOG_LEVEL=error

# Session configuration
SESSION_SECRET=your-production-session-secret-here

# Fake user password (if needed)
FAKE_USER_PASSWORD=your-fake-user-password-here

# Performance optimizations
# Disable source maps in production
NODE_OPTIONS="--max-old-space-size=4096"

# Additional Node.js performance flags
# Uncomment if needed:
# NODE_OPTIONS="--max-old-space-size=4096 --optimize-for-size"
