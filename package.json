{"name": "mppserver", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js"}, "dependencies": {"axios": "1.8.4", "better-sqlite3": "11.1.2", "deepl": "^1.0.13", "dotenv": "16.4.7", "events": "^3.3.0", "express": "5.0.1", "express-session": "^1.18.0", "keccak": "3.0.4", "node-json-color-stringify": "^1.1.0", "passport": "0.7.0", "passport-twitter": "1.0.4", "proxy-addr": "^2.0.7", "proxycheck-node.js": "2.0.0-a", "ws": "8.18.1"}, "packageManager": "pnpm@8.6.12+sha1.a2f983fbf8f2531dc85db2a5d7f398063d51a6f3"}