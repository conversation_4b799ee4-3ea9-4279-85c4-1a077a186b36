const ColorEncoder = require("./ColorEncoder.js")
const database = require("./Database.js")
const crypto = require("crypto")
class User {
    constructor(cl) {
        this.cl = cl
        this.server = this.cl.server
        this.default_db = {}
    }

    async getUserData() {
        const _id = createKeccakHash("keccak256")
            .update(this.cl.server._id_Private_Key + this.cl.ip)
            .digest("hex")
            .substr(0, 24)
        const uaerget = this.cl._id

        if (uaerget) {
            const usertofind = database.getUser(userget)

            if (usertofind) {
                for (const tag of usertofind.tag?.split(",") || []) {
                    if (tag === "OWNER") this.cl.owner = true
                    if (tag === "ADMIN") this.cl.admin = true
                    if (tag === "MOD") this.cl.mod = true
                    if (tag === "BOT") this.cl.bot = true
                    if (tag === "SPAMMER") this.cl.spammer = true
                }

                return {
                    color: usertofind.color,
                    name: usertofind.name,
                    _id: usertofind._id,
                    tag: usertofind.tag?.split(","),
                }
            }
        }

        const usertofind = database.getUser(_id)

        const DEFAULT_USER = {
            _id: _id,
            name: this.server.defaultUsername,
            color: `#${ColorEncoder.intToRGB(
                ColorEncoder.hashCode(_id)
            ).toLowerCase()}`,
            ip: this.cl.ip,
            tag: this.cl.ipv6 ? "IPv6" : null,
        }

        if (!usertofind) {
            if (
                typeof usertofind === "object" &&
                Object.hasOwn(usertofind, "name") &&
                usertofind.name !== this.server.defaultUsername
            )
                return
            if (!this.cl.fake) database.setUser(DEFAULT_USER)
        }

        const user = this.cl.fake ? DEFAULT_USER : database.getUser(_id)

        if (this.cl.fake) {
            const tags = ["SPAMMER", "FAKE"]
            user.tag = user.tag
                ? user.tag.split(",").concat(tags).join(",")
                : tags.join(",") //bad idk
        }

        for (const tag of user.tag?.split(",") || []) {
            if (tag === "OWNER") this.cl.owner = true
            if (tag === "ADMIN") this.cl.admin = true
            if (tag === "MOD") this.cl.mod = true
            if (tag === "BOT") this.cl.bot = true
            if (tag === "SPAMMER") this.cl.spammer = true
        }

        return {
            color: user.color,
            name: user.name,
            _id: user._id,
            tag: user.tag?.split(","),
        }
    }
}

module.exports = User
