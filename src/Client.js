const Room = require("./Room.js")
const Quota = require("./Quota.js")
const quotas = require("../Quotas")
const RateLimit = require("./Ratelimit.js").RateLimit
const RateLimitChain = require("./Ratelimit.js").RateLimitChain
const logger = require("./Logger.js")
const memoryMonitor = require("./MemoryMonitor.js")
// Removed expensive node-json-color-stringify for performance
// require("node-json-color-stringify")

function checkIPV6(str) {
    const regexExp =
        /(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))/gi
    return regexExp.test(str)
}

class Client extends EventEmitter {
    constructor(ws, req, server) {
        super()
        EventEmitter.call(this)
        this.user = {}
        this.connectionid = server.connectionid
        this.server = server
        this.participantId
        this.channel
        this.staticQuotas = {
            room: new RateLimit(quotas.room.time),
        }
        this.quotas = {}
        this.ws = ws
        this.req = req
        this.ip = req.ip.split(",")[0]
        this.ipv6 = checkIPV6(this.ip)
        this.fake = req.fake
        this.destroied = false

        // Message processing optimization
        this.messageQueue = []
        this.processingMessages = false
        this.maxQueueSize = 1000 // Prevent memory overflow

        this.bindEventListeners()
        require("./Message.js")(this)
    }
    isConnected() {
        return this.ws && this.ws.readyState === WebSocket.OPEN
    }
    isConnecting() {
        return this.ws && this.ws.readyState === WebSocket.CONNECTING
    }
    setChannel(_id, settings) {
        if (this.channel && this.channel._id === _id) return
        if (this.server.rooms.get(_id)) {
            const room = this.server.rooms.get(_id, settings)

            if (this.ipv6)
                room.Notification(
                    this.user?._id,
                    "IPv6 Detected",
                    "You are using an IPv6 address.\nNote: IPv6 is prone to change on a regular basis.",
                    "<p>meow</p>",
                    1e4,
                    "#piano"
                )

            let userbanned = room.bans.get(this.user?._id)
            if (
                userbanned &&
                Date.now() - userbanned.bannedtime >= userbanned.msbanned
            ) {
                room.bans.delete(userbanned.user?._id)
                userbanned = undefined
            }
            if (userbanned) {
                room.Notification(
                    this.user?._id,
                    "Notice",
                    `Currently banned from \"${_id}\" for ${Math.ceil(
                        Math.floor(
                            (userbanned.msbanned -
                                (Date.now() - userbanned.bannedtime)) /
                                1000
                        ) / 60
                    )} minutes.`,
                    7000,
                    "",
                    "#room",
                    "short"
                )
                this.setChannel("lobby", settings)
                return
            }
            const channel = this.channel
            if (channel) this.channel.emit("bye", this)
            if (channel) this.channel.updateCh()
            this.channel = this.server.rooms.get(_id)
            this.channel.join(this)
        } else {
            const room = new Room(this.server, _id, settings)
            this.server.rooms.set(_id, room)
            if (this.channel) this.channel.emit("bye", this)
            this.channel = this.server.rooms.get(_id)
            this.channel.join(this)
        }
    }
    sendRaw(arr) {
        if (this.isConnected()) {
            //console.log(SEND RAW: , arr));
            this.ws.send(arr)
        }
    }
    sendArray(arr) {
        if (this.isConnected()) {
            logger.debugWebSocket("SEND:", arr)
            this.ws.send(JSON.stringify(arr))
        }
    }
    initParticipantQuotas() {
        this.quotas = {
            //"chat": new Quota(Quota.PARAMS_A_NORMAL),
            chat: {
                lobby: new RateLimitChain(
                    quotas.chat.lobby.amount,
                    quotas.chat.lobby.time
                ),
                normal: new RateLimitChain(
                    quotas.chat.normal.amount,
                    quotas.chat.normal.time
                ),
                insane: new RateLimitChain(
                    quotas.chat.insane.amount,
                    quotas.chat.insane.time
                ),
            },
            cursor: new RateLimit(quotas.cursor.time),
            chown: new RateLimitChain(quotas.chown.amount, quotas.chown.time),
            userset: new RateLimitChain(
                quotas.userset.amount,
                quotas.userset.time
            ),
            kickban: new RateLimitChain(
                quotas.kickban.amount,
                quotas.kickban.time
            ),
            notelobby: new Quota(Quota.N_PARAMS_LOBBY),
            notenormal: new Quota(Quota.N_PARAMS_NORMAL),
            notecrown: new Quota(Quota.N_PARAMS_CROWN),
            chset: new Quota(Quota.PARAMS_USED_A_LOT),
            "+ls": new Quota(Quota.PARAMS_USED_A_LOT),
            "-ls": new Quota(Quota.PARAMS_USED_A_LOT),
        }
    }
    destroy() {
        if (this.destroied) return // Prevent double destruction

        this.destroied = true

        // Clean up WebSocket
        if (this.ws) {
            this.ws.removeAllListeners()
            if (this.ws.readyState === WebSocket.OPEN) {
                this.ws.close()
            }
        }

        // Clean up channel
        if (this.channel) {
            this.channel.emit("bye", this)
            this.channel = null
        }

        // Clean up server references
        this.server.roomlisteners.delete(this.connectionid)
        this.server.connections.delete(this.connectionid)

        // Update memory monitor with new connection count
        memoryMonitor.updateConnectionCount(this.server.connections.size)

        // Clean up quotas to prevent memory leaks
        this.quotas = null
        this.staticQuotas = null

        // Clear message queue
        this.messageQueue = []
        this.processingMessages = false

        // Clean up EventEmitter listeners
        this.removeAllListeners()

        // Clear references
        this.user = null
        this.participantId = null
        this.ws = null
        this.req = null
        this.server = null
    }
    // Process message queue with backpressure control
    async processMessageQueue() {
        if (this.processingMessages || this.destroied) return

        this.processingMessages = true

        while (this.messageQueue.length > 0 && !this.destroied) {
            const { evt, admin } = this.messageQueue.shift()

            try {
                await this.processMessage(evt, admin)
            } catch (error) {
                logger.error("Message processing error:", error)
            }

            // Yield control to event loop every 10 messages
            if (this.messageQueue.length % 10 === 0) {
                await new Promise((resolve) => setImmediate(resolve))
            }
        }

        this.processingMessages = false
    }

    async processMessage(evt, admin) {
        const message = evt.toString("utf8")

        try {
            const transmission = JSON.parse(message)

            for (const msg of transmission) {
                if (!Object.hasOwn(msg, "m")) return
                if (!this.server.legit_m.includes(msg.m)) return
                this.emit(msg.m, msg, !!admin)
                logger.debugMessage("RECEIVE:", msg)
            }
        } catch (jsonError) {
            this.emit("binary", evt, !!admin)
        }
    }

    bindEventListeners() {
        this.ws.on("message", (evt, admin) => {
            if (this.destroied) return

            // Implement backpressure - drop messages if queue is too full
            if (this.messageQueue.length >= this.maxQueueSize) {
                logger.warn(
                    `Message queue overflow for client ${this.connectionid}, dropping message`
                )
                return
            }

            this.messageQueue.push({ evt, admin })

            // Track message queue size for memory monitoring
            memoryMonitor.trackMessageQueue(
                this.connectionid,
                this.messageQueue.length
            )

            this.processMessageQueue()
        })

        this.ws.on("close", () => {
            if (!this.destroied) this.destroy()
        })

        this.ws.on("error", (err) => {
            logger.error("WebSocket error:", err)
            if (!this.destroied) this.destroy()
        })
    }
}
module.exports = Client
