const Room = require("./Room.js")
const Quota = require("./Quota.js")
const quotas = require("../Quotas")
const RateLimit = require("./Ratelimit.js").RateLimit
const RateLimitChain = require("./Ratelimit.js").RateLimitChain
require("node-json-color-stringify")

function checkIPV6(str) {
    const regexExp =
        /(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))/gi
    return regexExp.test(str)
}

class Client extends EventEmitter {
    constructor(ws, req, server) {
        super()
        EventEmitter.call(this)
        this.user = {}
        this.connectionid = server.connectionid
        this.server = server
        this.participantId
        this.channel
        this.staticQuotas = {
            room: new RateLimit(quotas.room.time),
        }
        this.quotas = {}
        this.ws = ws
        this.req = req
        this.ip = req.ip.split(",")[0]
        this.ipv6 = checkIPV6(this.ip)
        this.fake = req.fake
        this.destroied = false
        this.bindEventListeners()
        require("./Message.js")(this)
    }
    isConnected() {
        return this.ws && this.ws.readyState === WebSocket.OPEN
    }
    isConnecting() {
        return this.ws && this.ws.readyState === WebSocket.CONNECTING
    }
    setChannel(_id, settings) {
        if (this.channel && this.channel._id === _id) return
        if (this.server.rooms.get(_id)) {
            const room = this.server.rooms.get(_id, settings)

            if (this.ipv6)
                room.Notification(
                    this.user?._id,
                    "IPv6 Detected",
                    "You are using an IPv6 address.\nNote: IPv6 is prone to change on a regular basis.",
                    "<p>meow</p>",
                    1e4,
                    "#piano"
                )

            let userbanned = room.bans.get(this.user?._id)
            if (
                userbanned &&
                Date.now() - userbanned.bannedtime >= userbanned.msbanned
            ) {
                room.bans.delete(userbanned.user?._id)
                userbanned = undefined
            }
            if (userbanned) {
                room.Notification(
                    this.user?._id,
                    "Notice",
                    `Currently banned from \"${_id}\" for ${Math.ceil(
                        Math.floor(
                            (userbanned.msbanned -
                                (Date.now() - userbanned.bannedtime)) /
                                1000
                        ) / 60
                    )} minutes.`,
                    7000,
                    "",
                    "#room",
                    "short"
                )
                this.setChannel("lobby", settings)
                return
            }
            const channel = this.channel
            if (channel) this.channel.emit("bye", this)
            if (channel) this.channel.updateCh()
            this.channel = this.server.rooms.get(_id)
            this.channel.join(this)
        } else {
            const room = new Room(this.server, _id, settings)
            this.server.rooms.set(_id, room)
            if (this.channel) this.channel.emit("bye", this)
            this.channel = this.server.rooms.get(_id)
            this.channel.join(this)
        }
    }
    sendRaw(arr) {
        if (this.isConnected()) {
            //console.log(SEND RAW: , arr));
            this.ws.send(arr)
        }
    }
    sendArray(arr) {
        if (this.isConnected()) {
            //console.log(`SEND: `, JSON.colorStringify(arr));
            this.ws.send(JSON.stringify(arr))
        }
    }
    initParticipantQuotas() {
        this.quotas = {
            //"chat": new Quota(Quota.PARAMS_A_NORMAL),
            chat: {
                lobby: new RateLimitChain(
                    quotas.chat.lobby.amount,
                    quotas.chat.lobby.time
                ),
                normal: new RateLimitChain(
                    quotas.chat.normal.amount,
                    quotas.chat.normal.time
                ),
                insane: new RateLimitChain(
                    quotas.chat.insane.amount,
                    quotas.chat.insane.time
                ),
            },
            cursor: new RateLimit(quotas.cursor.time),
            chown: new RateLimitChain(quotas.chown.amount, quotas.chown.time),
            userset: new RateLimitChain(
                quotas.userset.amount,
                quotas.userset.time
            ),
            kickban: new RateLimitChain(
                quotas.kickban.amount,
                quotas.kickban.time
            ),
            notelobby: new Quota(Quota.N_PARAMS_LOBBY),
            notenormal: new Quota(Quota.N_PARAMS_NORMAL),
            notecrown: new Quota(Quota.N_PARAMS_CROWN),
            chset: new Quota(Quota.PARAMS_USED_A_LOT),
            "+ls": new Quota(Quota.PARAMS_USED_A_LOT),
            "-ls": new Quota(Quota.PARAMS_USED_A_LOT),
        }
    }
    destroy() {
        this.ws.close()
        if (this.channel) {
            this.channel.emit("bye", this)
        }
        this.user
        this.participantId
        this.channel
        this.server.roomlisteners.delete(this.connectionid)
        this.connectionid
        this.server.connections.delete(this.connectionid)
        this.destroied = true
    }
    bindEventListeners() {
        this.ws.on("message", (evt, admin) => {
            const message = evt.toString("utf8")

            try {
                const transmission = JSON.parse(message)

                for (const msg of transmission) {
                    if (!Object.hasOwn(msg, "m")) return
                    if (!this.server.legit_m.includes(msg.m)) return
                    this.emit(msg.m, msg, !!admin)
                    //console.log(`RECIEVE: `, JSON.colorStringify(msg));
                }
            } catch (jsonError) {
                this.emit("binary", evt, !!admin)
            }
        })
        this.ws.on("close", () => {
            if (!this.destroied) this.destroy()
        })
        this.ws.addEventListener("error", (err) => {
            console.error(err)
            if (!this.destroied) this.destroy()
        })
    }
}
module.exports = Client
