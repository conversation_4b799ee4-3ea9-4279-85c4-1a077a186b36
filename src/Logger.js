/**
 * Efficient logging system that can be disabled in production
 * to improve performance by avoiding expensive I/O operations
 */

class Logger {
    constructor() {
        this.isProduction = process.env.NODE_ENV === 'production'
        this.logLevel = process.env.LOG_LEVEL || (this.isProduction ? 'error' : 'debug')
        
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        }
        
        this.currentLevel = this.levels[this.logLevel] || this.levels.debug
    }
    
    shouldLog(level) {
        return this.levels[level] <= this.currentLevel
    }
    
    error(...args) {
        if (this.shouldLog('error')) {
            console.error('[ERROR]', ...args)
        }
    }
    
    warn(...args) {
        if (this.shouldLog('warn')) {
            console.warn('[WARN]', ...args)
        }
    }
    
    info(...args) {
        if (this.shouldLog('info')) {
            console.info('[INFO]', ...args)
        }
    }
    
    debug(...args) {
        if (this.shouldLog('debug')) {
            console.log('[DEBUG]', ...args)
        }
    }
    
    // Performance-optimized methods that do nothing in production
    debugWebSocket(...args) {
        if (!this.isProduction && this.shouldLog('debug')) {
            console.log('[WS-DEBUG]', ...args)
        }
    }
    
    debugMessage(...args) {
        if (!this.isProduction && this.shouldLog('debug')) {
            console.log('[MSG-DEBUG]', ...args)
        }
    }
    
    // Method to log JSON with optional pretty printing in development
    debugJSON(label, obj) {
        if (!this.isProduction && this.shouldLog('debug')) {
            console.log(`[JSON-DEBUG] ${label}:`, JSON.stringify(obj, null, 2))
        }
    }
}

// Create singleton instance
const logger = new Logger()

module.exports = logger
