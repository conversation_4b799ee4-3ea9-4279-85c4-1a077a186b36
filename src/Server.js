const Client = require("./Client.js")
const banned = require("../banned.json")
const proxyaddr = require("proxy-addr")
const session = require("express-session")
const passport = require("passport")

class Server extends EventEmitter {
    constructor(config) {
        super()
        EventEmitter.call(this)

        const TRUST_PROXIES = [
            "0.0.0.0/8",
            "127.0.0.1/8",
            "192.168.3.4/24",
            "173.245.48.0/20",
            "103.21.244.0/22",
            "103.22.200.0/22",
            "103.31.4.0/22",
            "141.101.64.0/18",
            "108.162.192.0/18",
            "190.93.240.0/20",
            "188.114.96.0/20",
            "197.234.240.0/22",
            "198.41.128.0/17",
            "162.158.0.0/15",
            "104.16.0.0/13",
            "104.24.0.0/14",
            "172.64.0.0/13",
            "131.0.72.0/22",
            "2400:cb00::/32",
            "2606:4700::/32",
            "2803:f800::/32",
            "2405:b500::/32",
            "2405:8100::/32",
            "2a06:98c0::/29",
            "2c0f:f248::/32",
        ]

        this.app = express()
        this.app.set("env", "production")
        this.app.set("trust proxy", TRUST_PROXIES)
        this.app.use((req, res, next) => {
            res.setHeader("Access-Control-Allow-Origin", "https://mpp.c30.life")
            if (banned.includes(req.ip.replace("::ffff:", ""))) {
                const user = sql
                    .prepare("SELECT * FROM users WHERE ip = ?;")
                    .get(req.ip)
                res.status(403).send(
                    `You are banned from this server.<br>If you think this is a mistake, please contact the server owner.<br>Your IP: ${req.ip}<br>Your _id: ${user._id}`
                )
                return res.end()
            }
            next()
        })
        this.app.use(
            session({
                secret: process.env.SESSION_SECRET,
                cookie: { maxAge: 60000 },
                resave: false,
                saveUninitialized: false,
            })
        )
        this.app.use(passport.initialize())
        this.app.use(passport.session())
        this.app.use(express.urlencoded({ extended: false }))
        this.app.use(express.json())
        this.app.use(express.text())
        this.app.use(express.static("public"))
        this.app.use("/oauth", require("../router/oauth.js"))
        this.app.use("/admin", require("../router/admin.js"))

        this.httpServer = http.createServer(this.app)
        this.wss = new WebSocket.Server({
            server: this.httpServer,
            backlog: 100,
            verifyClient: (info) => {
                const ip = proxyaddr(info.req, this.app.get("trust proxy")) //maybe dont need fakeuser ip here
                return !banned.includes(ip.replace("::ffff:", ""))
            },
        })
        this.connectionid = 0
        this.connections = new Map()
        this.roomlisteners = new Map()
        this.rooms = new Map()
        this.wss.on("connection", (ws, req) => {
            const fakeUser =
                Object.hasOwn(req.headers, "password") &&
                req.headers.password === process.env.FAKE_USER_PASSWORD
            if (fakeUser) {
                if (Object.hasOwn(req.headers, "x-forwarded-for"))
                    req.ip = req.headers["x-forwarded-for"]
            } else {
                req.ip = proxyaddr(req, this.app.get("trust proxy"))
            }
            req.fake = fakeUser //idk
            this.connections.set(++this.connectionid, new Client(ws, req, this))
        })
        this.legit_m = [
            "a",
            "bye",
            "hi",
            "ch",
            "+ls",
            "-ls",
            "m",
            "n",
            "devices",
            "t",
            "chset",
            "userset",
            "chown",
            "kickban",
            "admin message",
            "color",
            "draw",
            "erase",
            "notification",
        ]
        this._id_Private_Key = config._id_PrivateKey || ""
        this.defaultUsername = config.defaultUsername || "Anonymous"
        this.defaultRoomColor = config.defaultRoomColor || "#ff7f00"
        this.defaultRoomColor2 = config.defaultRoomColor2 || "#bf3f00"
        this.adminpass = config.adminpass || ""

        this.httpServer.listen(config.port, () => {
            console.log(`MPP Server started on port ${config.port}`)
        })
    }
    updateRoom(data) {
        if (!data.ch.settings.visible) return
        for (const cl of Array.from(this.roomlisteners.values())) {
            cl.sendArray([
                {
                    m: "ls",
                    c: false,
                    u: [data.ch],
                },
            ])
        }
    }
}

module.exports = Server
