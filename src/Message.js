const Quota = require("./Quota")
const User = require("./User.js")
const database = require("./Database.js")

module.exports = (cl) => {
    cl.once("hi", async (data) => {
        const user = new User(cl)
        const uData = await user.getUserData()

        if (!user) {
            cl.sendArray([
                {
                    m: "notification",
                    duration: 6000,
                    title: "User not found",
                    text: "Reason: where user.",
                    target: "#room",
                },
            ])
            setTimeout(() => cl.destroy(), 6000)
            return
        }
        const msg = {}
        msg.m = "hi"
        msg.t = Date.now()
        msg.u = uData
        msg.v = "2.0"
        cl.sendArray([msg])
        cl.user = uData
        return
    })
    cl.on("binary", (msg) => {
        if (!(cl.channel && cl.participantId)) return

        if (cl.channel.settings.crownsolo) {
            if (
                cl.channel.crown.userId === cl.user._id &&
                !cl.channel.crowndropped
            ) {
                cl.channel.playBinaryNote(cl, msg)
            }
        } else {
            cl.channel.playBinaryNote(cl, msg)
        }

        //console.log("Binary!!!!    | ", msg);
    })
    cl.on("t", (msg) => {
        if (Object.hasOwn(msg, "e") && !Number.isNaN(msg.e))
            cl.sendArray([
                {
                    m: "t",
                    t: Date.now(),
                    e: msg.e,
                },
            ])
    })
    cl.on("ch", (msg) => {
        if (!Object.hasOwn(msg, "set") || !msg.set) msg.set = {}
        if (Object.hasOwn(msg, "_id") && typeof msg._id === "string") {
            if (!msg._id || /^\s*$/.test(msg._id)) return
            if (msg._id.length > 512) return
            if (!cl.staticQuotas.room.attempt()) return
            cl.setChannel(msg._id, msg.set)
            let param = {}
            param.m = "nq"
            if (cl.channel.isLobby(cl.channel._id)) {
                if (cl.spammer) {
                    param.max = Number.MAX_SAFE_INTEGER
                    param.allowance = Number.MAX_SAFE_INTEGER
                } else {
                    param.max = Quota.N_PARAMS_LOBBY
                    param.allowance = Quota.N_PARAMS_LOBBY
                }

                cl.sendArray([param])
            } else {
                const nqValue = !(cl.user._id === cl.channel.crown.userId)

                param = nqValue ? Quota.N_PARAMS_NORMAL : Quota.N_PARAMS_CROWN
                if (cl.spammer) {
                    param = Number.MAX_SAFE_INTEGER
                    param.allowance = Number.MAX_SAFE_INTEGER
                } else {
                    param = nqValue
                        ? Quota.N_PARAMS_NORMAL
                        : Quota.N_PARAMS_CROWN
                    param.allowance = nqValue
                        ? Quota.N_PARAMS_NORMAL
                        : Quota.N_PARAMS_CROWN
                }

                cl.sendArray([param])
            }
        }
    })
    cl.on("m", (msg, admin) => {
        if (!cl.quotas.cursor && !admin) return
        if (!(cl.channel && cl.participantId)) return
        if (!Object.hasOwn(msg, "x")) msg.x = null
        if (!Object.hasOwn(msg, "y")) msg.y = null
        if (Number.isNaN(Number.parseInt(msg.x))) msg.x = null
        if (Number.isNaN(Number.parseInt(msg.y))) msg.y = null
        cl.channel.emit("m", cl, msg.x, msg.y)
    })
    cl.on("chown", (msg, admin) => {
        if (!cl.quotas.chown.attempt() && !admin) return
        if (!(cl.channel && cl.participantId)) return
        if (
            !(cl.channel.crown.userId === cl.user._id) &&
            !(Date.now() - cl.channel.crown.time > 15000)
        )
            return
        let param
        if (Object.hasOwn(msg, "id")) {
            if (
                cl.user._id === cl.channel.crown.userId ||
                cl.channel.crowndropped
            )
                cl.channel.chown(msg.id)
            if (msg.id === cl.user.id) {
                param = Quota.N_PARAMS_NORMAL
                param.m = "nq"
                cl.sendArray([param])
            }
        } else {
            if (
                cl.user._id === cl.channel.crown.userId ||
                cl.channel.crowndropped
            )
                cl.channel.chown()
            param = Quota.N_PARAMS_NORMAL
            param.m = "nq"
            cl.sendArray([param])
        }
    })
    cl.on("chset", (msg) => {
        if (!(cl.channel && cl.participantId)) return
        if (!(cl.user._id === cl.channel.crown.userId)) return
        if (!Object.hasOwn(msg, "set") || !msg.set)
            msg.set = cl.channel.verifySet(cl.channel._id, {})
        cl.channel.settings = msg.set
        cl.channel.updateCh()
    })
    cl.on("a", (msg, admin) => {
        if (!(cl.channel && cl.participantId)) return
        if (!Object.hasOwn(msg, "message")) return
        if (!msg.message || /^\s*$/.test(msg.message)) return
        if (cl.channel.settings.chat) {
            if (cl.channel.isLobby(cl.channel._id)) {
                if (!cl.quotas.chat.lobby.attempt() && !admin) return
            } else {
                if (!(cl.user._id === cl.channel.crown.userId)) {
                    if (!cl.quotas.chat.normal.attempt() && !admin) return
                } else {
                    if (!cl.quotas.chat.insane.attempt() && !admin) return
                }
            }
            cl.channel.emit("a", cl, msg)
        }
    })
    cl.on("n", (msg) => {
        if (!(cl.channel && cl.participantId))
            return console.log("no channel or participantId")
        if (!Object.hasOwn(msg, "t") || !Object.hasOwn(msg, "n"))
            return console.log("no t or n")
        if (typeof msg.t !== "number" || typeof msg.n !== "object")
            return console.log("t not number or n not object")

        if (!Array.isArray(msg.n)) return console.log("n not array")

        for (const note of msg.n) {
            if (typeof note.n !== "string") return
            if (note.n.length > 5) return
            if (typeof note.d === "number") {
                if (Number.isNaN(note.d)) return
                if (note.d > 2000 || note.d < -100) return
            } else if (note.d !== undefined) {
                return
            }

            if (typeof note.v === "number") {
                if (Object.hasOwn(note, "s")) return
                if (Number.isNaN(note.v)) return
                if (note.v > 1.27) return
                if (note.v < 0) return
            } else if (note.s === 1) {
                if (note.v !== undefined) return
            } else return
            if (note.c && Number.isNaN(Number.parseInt(note?.c?.slice(1), 16)))
                return
        }
        if (cl.channel.settings.crownsolo) {
            if (
                cl.channel.crown.userId === cl.user._id &&
                !cl.channel.crowndropped
            ) {
                cl.channel.playNote(cl, msg)
            }
        } else {
            cl.channel.playNote(cl, msg)
        }
    })
    cl.on("+ls", () => {
        if (!(cl.channel && cl.participantId)) return
        cl.server.roomlisteners.set(cl.connectionid, cl)
        const rooms = []
        for (const room of Array.from(cl.server.rooms.values())) {
            const data = room.fetchData().ch
            if (room.bans.get(cl.user._id)) {
                data.banned = true
            }
            if (room.settings.visible) rooms.push(data)
        }
        cl.sendArray([
            {
                m: "ls",
                c: true,
                u: rooms,
            },
        ])
    })
    cl.on("-ls", () => {
        if (!(cl.channel && cl.participantId)) return
        cl.server.roomlisteners.delete(cl.connectionid)
    })
    cl.on("userset", (msg) => {
        if (!(cl.channel && cl.participantId)) return
        if (!Object.hasOwn(msg, "set") || !msg.set) msg.set = {}

        if (
            Object.hasOwn(msg.set, "name") &&
            typeof msg.set.name === "string"
        ) {
            if (!msg.set.name || /^\s*$/.test(msg.set.name)) return
            if (msg.set.name.length > 40) return
            cl.user.name = msg.set.name

            const user = new User(cl)
            user.getUserData().then(() => {
                if (!cl.fake)
                    database.setUser({
                        _id: cl.user._id,
                        name: cl.user.name,
                        color: cl.user.color,
                        ip: cl.ip,
                        tag: cl.user.tag?.join(","),
                    })
                for (const room of cl.server.rooms.values()) {
                    room.updateParticipant(cl.user._id, {
                        name: msg.set.name,
                        tag: cl.user.tag,
                    })
                }
            })
        }
        if (
            Object.hasOwn(msg.set, "color") &&
            typeof msg.set.color === "string"
        ) {
            if (
                !isString(msg.set.color) ||
                !/^#[0-9a-f]{6}$/i.test(msg.set.color)
            )
                return
            cl.user.color = msg.set.color
            const user = new User(cl)
            user.getUserData().then(() => {
                if (!cl.fake)
                    database.setUser({
                        _id: cl.user._id,
                        name: cl.user.name || "fuck you error user",
                        color: cl.user.color,
                        ip: cl.ip,
                        tag: cl.user.tag?.join(","),
                    })
                for (const room of cl.server.rooms.values()) {
                    room.updateParticipant(cl.user._id, {
                        color: msg.set.color,
                        tag: cl.user.tag,
                    })
                }
            })
        }
        if (Object.hasOwn(msg.set, "afk") && typeof msg.set.afk === "string") {
            if (["yes", "no"].includes(msg.set.afk)) {
                cl.user.afk = msg.set.afk
                const user = new User(cl)
                user.getUserData().then(() => {
                    for (const room of cl.server.rooms.values()) {
                        room.updateParticipant(cl.user._id, {
                            afk: msg.set.afk,
                            tag: cl.user.tag,
                        })
                    }
                })
            }
        }
    })
    cl.on("kickban", (msg) => {
        if (cl.channel.crown == null) return
        if (!cl.channel && cl.participantId) return
        if (!cl.channel.crown.userId) return
        if (!cl.user._id === cl.channel.crown.userId) return
        if (Object.hasOwn(msg, "_id") && typeof msg._id === "string") {
            if (!cl.quotas.kickban.attempt()) return
            const _id = msg._id
            const ms = msg.ms || 3600000
            cl.channel.kickban(_id, ms)
        }
    })
    cl.on("bye", () => {
        cl.destroy()
    })
    cl.on("admin message", (msg) => {
        if (!(cl.channel && cl.participantId)) return
        if (!Object.hasOwn(msg, "password") || !Object.hasOwn(msg, "msg"))
            return
        if (typeof msg.msg !== "object") return
        if (msg.password !== cl.server.adminpass) return
        cl.ws.emit("message", JSON.stringify([msg.msg]), true)
    })
    cl.on("draw", (msg) => {
        if (!(cl.channel && cl.participantId)) return
        if (!Object.hasOwn(msg, "t")) return
        if (typeof msg.t !== "number") return
        cl.channel.draw(cl, msg)
    })
    //admin only stuff
    cl.on("erase", (msg, admin) => {
        if (!admin) return
        if (!Object.hasOwn(msg, "id")) return
        cl.channel.erase(cl, msg)
    })
    cl.on("color", (msg, admin) => {
        if (!admin) return
        if (typeof cl.channel.verifyColor(msg.color) !== "string") return
        if (!Object.hasOwn(msg, "id") && !Object.hasOwn(msg, "_id")) return
        for (const usr of cl.server.connections) {
            if (
                usr.channel &&
                usr.participantId &&
                usr.user &&
                (usr.user._id === msg._id || usr.participantId === msg.id)
            ) {
                const user = new User(usr)
                user.cl.user.color = msg.color
                user.getUserData().then(() => {
                    database.setUser({
                        _id: cl.user._id,
                        name: cl.user.name,
                        color: cl.user.color,
                        ip: cl.ip,
                        tag: cl.user.tag?.join(","),
                    })
                    for (const room of cl.server.rooms) {
                        room.updateParticipant(cl.user._id, {
                            color: msg.color,
                            tag: cl.user.tag,
                        })
                    }
                })
            }
        }
    })

    // Can be used as announcements for people on the server. For exmaple like mppclone.com.
    cl.on("notification", (msg, admin) => {
        if (!admin) return
        try {
            cl.channel.Notification(
                msg.settings?.who,
                msg.settings?.title,
                msg.settings?.text,
                msg.settings?.html,
                msg.settings?.duration,
                msg.settings?.target,
                msg.settings?.klass,
                msg.settings?.id
            )
        } catch (e) {}
    })
}
