//array of rooms
//room class
//room deleter
//databases in Map
const axios = require("axios")
const fs = require("node:fs")
const logger = require("./Logger.js")

class Room extends EventEmitter {
    constructor(server, _id, settings) {
        super()
        EventEmitter.call(this)
        this._id = _id
        this.server = server
        this.crown = null
        this.crowndropped = false
        this.settings = this.verifySet(this._id, {
            set: settings,
        })
        this.chatmsgs = []
        this.ppl = new Map()
        this.connections = []
        this.bindEventListeners()
        this.server.rooms.set(_id, this)
        this.bans = new Map()
    }
    join(cl) {
        //this stuff is complicated
        const otheruser = this.connections.find(
            (a) => a.user._id === cl.user?._id
        )
        if (!otheruser) {
            const participantId = createKeccakHash("keccak256")
                .update(Math.random().toString() + cl.ip)
                .digest("hex")
                .substr(0, 24)
            if (!cl.user) {
                this.Notification(
                    "room",
                    "oof",
                    "maybe this is bug...",
                    "",
                    7000,
                    "#room",
                    "short"
                )
                cl.destroy()
                return
            }
            cl.user.id = participantId
            cl.participantId = participantId
            cl.initParticipantQuotas()
            //console.log(`New client(${cl.user._id}): ${cl.ip}`);
            if (
                (this.connections.length === 0 &&
                    Array.from(this.ppl.values()).length === 0 &&
                    !this.isLobby(this._id)) ||
                (this.crown && this.crown.userId === cl.user._id)
            ) {
                //user that created the room, give them the crown.
                this.crown = {
                    participantId: cl.participantId,
                    userId: cl.user._id,
                    time: Date.now(),
                    startPos: {
                        x: 50,
                        y: 50,
                    },
                    endPos: {
                        x: this.getCrownX(),
                        y: this.getCrownY(),
                    },
                }
                this.crowndropped = false
                this.settings = {
                    visible: !!this.settings.visible,
                    color: this.settings.color
                        ? this.settings.color
                        : this.server.defaultRoomColor,
                    color2: this.settings.color2
                        ? this.settings.color2
                        : this.server.defaultRoomColor2,
                    chat: true,
                    crownsolo: false,
                }
            } else {
                this.settings = {
                    visible: !!this.settings.visible,
                    color: this.settings.color
                        ? this.settings.color
                        : this.server.defaultRoomColor,
                    color2: this.settings.color2
                        ? this.settings.color2
                        : this.server.defaultRoomColor2,
                    chat: true,
                    crownsolo: false,
                    lobby: this.isLobby(this._id),
                }
            }
            this.ppl.set(participantId, cl)

            this.connections.push(cl)
            this.sendArray(
                [
                    {
                        color: this.ppl.get(cl.participantId).user.color,
                        id: this.ppl.get(cl.participantId).participantId,
                        m: "p",
                        name: this.ppl.get(cl.participantId).user.name,
                        x: this.ppl.get(cl.participantId).x || 200,
                        y: this.ppl.get(cl.participantId).y || 100,
                        _id: cl.user._id,
                        tag: cl.user.tag,
                    },
                ],
                cl,
                false
            )
            cl.sendArray([
                {
                    m: "c",
                    c: this.chatmsgs.slice(-1 * 32),
                },
            ])
            this.updateCh(cl, this.settings)
        } else {
            cl.user.id = otheruser.participantId
            cl.participantId = otheruser.participantId
            cl.quotas = otheruser.quotas
            this.connections.push(cl)
            cl.sendArray([
                {
                    m: "c",
                    c: this.chatmsgs.slice(-1 * 32),
                },
            ])
            this.updateCh(cl, this.settings)
        }
    }
    remove(p) {
        //this is complicated too
        const otheruser = this.connections.filter(
            (a) => a.user._id === p.user?._id
        )

        //console.log(`Deleted client: ${p.ip}`);
        if (!(otheruser.length > 1)) {
            this.ppl.delete(p.participantId)
            this.connections.splice(
                this.connections.findIndex(
                    (a) => a.connectionid === p.connectionid
                ),
                1
            )
            this.sendArray(
                [
                    {
                        m: "bye",
                        p: p.participantId,
                    },
                ],
                p,
                false
            )
            if (this.crown)
                if (this.crown.userId === p.user._id && !this.crowndropped) {
                    this.chown()
                }
            this.updateCh()
        } else {
            this.connections.splice(
                this.connections.findIndex(
                    (a) => a.connectionid === p.connectionid
                ),
                1
            )
        }
    }
    updateCh(cl) {
        //update channel for all people in channel
        if (Array.from(this.ppl.values()).length <= 0) this.destroy()
        for (const usr of this.connections) {
            this.server.connections
                .get(usr.connectionid)
                .sendArray([this.fetchData(usr, cl)])
        }
        this.server.updateRoom(this.fetchData())
    }
    updateParticipant(pid, options) {
        let p = null
        Array.from(this.ppl).map((rpg) => {
            if (rpg[1].user._id === pid) p = rpg[1]
        })
        if (p == null) return

        if (options.name) p.user.name = options.name
        if (options._id) p.user._id = options._id
        if (options.color) p.user.color = options.color
        if (options.afk) p.user.afk = options.afk
        const filteredConnections = this.connections.filter(
            (ofo) => ofo.participantId === p.participantId
        )
        for (const usr of filteredConnections) {
            if (options.name) usr.user.name = options.name
            if (options._id) usr.user._id = options._id
            if (options.color) usr.user.color = options.color
            if (options.afk) usr.user.afk = options.afk
        }

        this.sendArray([
            {
                color: p.user.color,
                id: p.participantId,
                m: "p",
                name: p.user.name,
                x: p.x || 200,
                y: p.y || 100,
                afk: p.user.afk,
                _id: p.user._id,
            },
        ])
    }
    destroy() {
        //destroy room
        this._id
        this.settings = {}
        this.ppl
        this.connnections
        this.chatmsgs
        this.server.rooms.delete(this._id)
    }
    send(arr, not, onlythisparticipant) {
        for (const usr of this.connections) {
            if (
                !not ||
                (usr.participantId !== not.participantId &&
                    !onlythisparticipant) ||
                (usr.connectionid !== not.connectionid && onlythisparticipant)
            ) {
                try {
                    this.server.connections.get(usr.connectionid)?.sendRaw(arr)
                } catch (e) {
                    logger.error("WebSocket send error:", e)
                }
            }
        }
    }
    sendArray(arr, not, onlythisparticipant) {
        for (const usr of this.connections) {
            if (
                !not ||
                (usr.participantId !== not.participantId &&
                    !onlythisparticipant) ||
                (usr.connectionid !== not.connectionid && onlythisparticipant)
            ) {
                try {
                    this.server.connections
                        .get(usr.connectionid)
                        ?.sendArray(arr)
                } catch (e) {
                    logger.error("WebSocket send error:", e)
                }
            }
        }
    }
    fetchData(usr, cl) {
        const chppl = []
        for (const a of this.ppl.values()) {
            chppl.push(a.user)
        }
        const data = {
            m: "ch",
            ch: {
                count: chppl.length,
                crown: this.crown,
                settings: this.settings,
                _id: this._id,
            },
            ppl: chppl,
        }
        if (cl) {
            if (usr.connectionid === cl.connectionid) {
                data.p = cl.participantId
            } else {
                data.p = undefined
            }
        } else {
            data.p = undefined
        }
        if (data.ch.crown == null) {
            data.ch.crown = undefined
        } else {
        }
        return data
    }
    verifyColor(strColor) {
        const test2 = /^#[0-9A-F]{6}$/i.test(strColor)
        if (test2 === true) {
            return strColor
        }
        return false
    }
    isLobby(_id) {
        if (_id.startsWith("lobby")) {
            const lobbynum = _id.split("lobby")[1]
            if (_id === "lobby") {
                return true
            }
            if (!(Number.parseInt(lobbynum).toString() === lobbynum))
                return false
            for (const i in lobbynum) {
                if (Number.parseInt(lobbynum[i]) >= 0) {
                    if (Number.parseInt(i) + 1 === lobbynum.length) return true
                } else {
                    return false
                }
            }
        } else if (_id.startsWith("test/")) {
            if (_id === "test/") {
                return false
            }
            return true
        } else {
            return false
        }
    }
    getCrownY() {
        return 50 - 30
    }
    getCrownX() {
        return 50
    }
    chown(id) {
        const prsn = this.ppl.get(id)
        if (prsn) {
            this.crown = {
                participantId: prsn.participantId,
                userId: prsn.user._id,
                time: Date.now(),
                startPos: {
                    x: 50,
                    y: 50,
                },
                endPos: {
                    x: this.getCrownX(),
                    y: this.getCrownY(),
                },
            }
            this.crowndropped = false
        } else {
            this.crown = {
                userId: this.crown.userId,
                time: Date.now(),
                startPos: {
                    x: 50,
                    y: 50,
                },
                endPos: {
                    x: this.getCrownX(),
                    y: this.getCrownY(),
                },
            }
            this.crowndropped = true
        }
        this.updateCh()
    }
    setCords(p, x, y) {
        if (p.participantId && this.ppl.get(p.participantId)) {
            if (x) this.ppl.get(p.participantId).x = x
            if (y) this.ppl.get(p.participantId).y = y
            this.sendArray(
                [
                    {
                        m: "m",
                        id: p.participantId,
                        x: this.ppl.get(p.participantId).x,
                        y: this.ppl.get(p.participantId).y,
                    },
                ],
                p,
                false
            )
        }
    }
    async chat(p, msg) {
        if (msg.message.length > 512) return
        if (
            [
                "I AM DJDAN AND I WILL REPORT LAPIS",
                "I AM LAPIS AND I WATCH PORN",
                "BAN ME",
            ].some((m) => {
                if (typeof msg?.message === "string")
                    msg?.message?.startsWith(m)
            })
        ) {
            const ppl = this.ppl.get(p.participantId)

            const banned = require("../banned.json")
            banned.push(ppl.ip)
            // Use async file write to avoid blocking the event loop
            fs.writeFile("./banned.json", JSON.stringify(banned), (err) => {
                if (err) logger.error("Failed to write banned.json:", err)
            })

            this.Notification(
                "all",
                `Auto banned ${ppl.user.name}`,
                `${ppl.user.name} was automatically banned from the site.`,
                "",
                3000,
                "#room"
            )

            ppl.sendArray([
                {
                    m: "notification",
                    who: ppl.user._id,
                    title: "Auto banned",
                    html: `<p>You were automatically banned from the site.</p><br><input type="range" id="timeout" min="0" max="100" value="100"><script>let i=100;(function abcbc(){setTimeout(()=>{if(i===0)return location.reload();document.getElementById("timeout").value = i--;abcbc();},30);})();</script>`,
                    duration: 3000,
                    target: "#piano",
                },
            ])
            ppl.destroy()
            return
        }

        const prsn = this.ppl.get(p.participantId)
        if (prsn) {
            const message = {}
            message.m = "a"
            message.a = msg.message
            message.p = {
                color: p.user.color,
                id: p.participantId,
                name: p.user.name,
                _id: p.user._id,
                tag: p.user.tag,
                afk: p.user.afk,
            }

            message.tl = {
                text: "[NO] Translate soon...",
            }

            message.t = Date.now()
            this.sendArray([message])
            this.chatmsgs.push(message)
            require("./Bot.js")(message, this)
        }
    }
    playNote(cl, note) {
        const spammerQuota = new (require("./Quota"))({
            allowance: 1920 * 8,
            max: 3840 * 8,
            maxHistLen: 6000,
        })

        if (cl.channel.isLobby(cl.channel._id)) {
            if (
                !(cl.spammer
                    ? spammerQuota.spend(note.n.length)
                    : cl.quotas.notelobby.spend(note.n.length))
            )
                return
        } else {
            if (!(cl.user._id === cl.channel.crown.userId)) {
                if (
                    !(cl.spammer
                        ? spammerQuota.spend(note.n.length)
                        : cl.quotas.notenormal.spend(note.n.length))
                )
                    return
            } else {
                if (
                    !(cl.spammer
                        ? spammerQuota.spend(note.n.length)
                        : cl.quotas.notecrown.spend(note.n.length))
                )
                    return
            }
        }
        this.sendArray(
            [
                {
                    m: "n",
                    n: note.n,
                    p: cl.participantId,
                    t: note.t,
                },
            ],
            cl,
            true
        )
    }
    playBinaryNote(p, noteData) {
        if (noteData.length < 12) return

        // Apply filter
        for (let i = 9; i < noteData.length; i += 3) {
            if (noteData.readUInt8(i) > 87) {
                return
            }
        }

        const userId = p.participantId

        const buffer = Buffer.alloc(noteData.length + 8)
        buffer.writeUInt8(1, 0)
        buffer.writeBigUInt64LE(userId, 1)
        noteData.copy(buffer, 9)

        this.send(buffer, p)
    }
    draw(cl, note) {
        // Only log large drawing operations in debug mode
        logger.debug("Large drawing operation:", note.n.length, "points")
        this.sendArray(
            [
                {
                    m: "draw",
                    n: note.n,
                    p: cl.participantId,
                    t: note.t,
                },
            ],
            cl,
            true
        )
    }
    erase(cl, msg) {
        for (const con of this.connections) {
            con.sendArray(
                [
                    {
                        m: "erase",
                        p: msg.id,
                    },
                ],
                cl,
                true
            )
        }
    }
    kickban(_id, ms) {
        let banTime = Number.parseInt(ms)
        if (banTime >= 1000 * 60 * 60) return
        if (banTime === 0) return 3600000
        if (banTime < 0) return
        banTime = Math.round(banTime / 1000) * 1000
        const user = this.connections.find((usr) => usr.user._id === _id)
        if (!user) return
        const pthatbanned = this.ppl.get(this.crown.participantId)
        const filteredConnections = this.connections.filter(
            (usr) => usr.participantId === user.participantId
        )
        for (const u of filteredConnections) {
            user.bantime = Math.floor(Math.floor(banTime / 1000) / 60)
            user.bannedtime = Date.now()
            user.msbanned = banTime
            this.bans.set(user.user._id, user)
            if (this.crown?.userId) {
                if (this.crown && this.crown.userId === _id) {
                    this.Notification(
                        "room",
                        "Certificate of Award",
                        `Let it be known that ${user.user.name} kickbanned him/her self.`,
                        "",
                        7000,
                        "#room"
                    )
                    break
                }
                u.setChannel("lobby", {})
                this.Notification(
                    user.user._id,
                    "Notice",
                    `Banned from \"${this._id}\" for ${Math.floor(
                        Math.floor(banTime / 1000) / 60
                    )} minutes.`,
                    "",
                    7000,
                    "#room",
                    "short"
                )
                this.Notification(
                    "room",
                    "Notice",
                    `${pthatbanned.user.name} banned ${
                        user.user.name
                    } from the channel for ${Math.floor(
                        Math.floor(banTime / 1000) / 60
                    )} minutes.`,
                    "",
                    7000,
                    "#room",
                    "short"
                )
            }
        }
    }
    Notification(who, title, text, html, duration, target, klass, id) {
        const obj = {
            m: "notification",
            title: title,
            text: text,
            html: html,
            target: target,
            duration: duration,
            class: klass,
            id: id,
        }
        if (!id) obj.id = undefined
        if (!title) obj.title = undefined
        if (!text) obj.text = undefined
        if (!html) obj.html = undefined
        if (!target) obj.target = undefined
        if (!duration) obj.duration = undefined
        if (!klass) obj.class = undefined
        switch (who?.toLowerCase()) {
            case "all": {
                for (const con of Array.from(
                    this.server.connections.values()
                )) {
                    con.sendArray([obj])
                }
                break
            }
            case "room": {
                for (const con of this.connections) {
                    con.sendArray([obj])
                }
                break
            }
            default: {
                for (const p of Array.from(
                    this.server.connections.values()
                ).filter((usr) => usr.user?._id === who)) {
                    p.sendArray([obj])
                }
            }
        }
    }
    bindEventListeners() {
        this.on("bye", (participant) => {
            this.remove(participant)
        }) //i afk i soon back

        this.on("m", (participant, x, y) => {
            this.setCords(participant, x, y)
        })

        this.on("a", (participant, msg) => {
            this.chat(participant, msg)
        })
    }
    verifySet(_id, msg) {
        if (!isObj(msg.set))
            msg.set = {
                visible: true,
                color: this.server.defaultRoomColor,
                color2: defaultRoomColor2,
                chat: true,
                crownsolo: false,
            }
        if (isBool(msg.set.lobby)) {
            if (!this.isLobby(_id)) msg.set.lobby // keep it nice and clean = undefined // keep it nice and clean
        } else {
            if (this.isLobby(_id))
                msg.set = {
                    visible: true,
                    color: this.server.defaultRoomColor,
                    color2: this.server.defaultRoomColor2,
                    chat: true,
                    crownsolo: false,
                    lobby: true,
                }
        }
        if (!isBool(msg.set.visible)) {
            if (msg.set.visible === undefined)
                msg.set.visible = !isObj(this.settings)
                    ? true
                    : this.settings.visible
            else msg.set.visible = true
        }
        if (!isBool(msg.set.chat)) {
            if (msg.set.chat === undefined)
                msg.set.chat = !isObj(this.settings) ? true : this.settings.chat
            else msg.set.chat = true
        }
        if (!isBool(msg.set.crownsolo)) {
            if (msg.set.crownsolo === undefined)
                msg.set.crownsolo = !isObj(this.settings)
                    ? false
                    : this.settings.crownsolo
            else msg.set.crownsolo = false
        }
        if (!isString(msg.set.color) || !/^#[0-9a-f]{6}$/i.test(msg.set.color))
            msg.set.color = !isObj(this.settings)
                ? this.server.defaultRoomColor
                : this.settings.color
        if (!isString(msg.set.color2)) {
            if (!/^#[0-9a-f]{6}$/i.test(msg.set.color2)) {
                if (this.settings) {
                    if (this.settings.color2)
                        msg.set.color2 = this.settings.color2
                    else msg.set.color2 // keep it nice and clean = undefined // keep it nice and clean
                } else {
                    msg.set.color2 = undefined
                }
            }
        }
        return msg.set
    }
}

module.exports = Room

// for (const p of Array.from(this.server.connections.values()).filter(
//     (usr) => usr.user?._id === who
// )) {
//     p.sendArray([obj])
// }
