const { execSync } = require("node:child_process")
const fs = require("node:fs")

const trusted = ["OWNER", "ADMIN"]
const prefix = "//"
const commands = {
    restart(data) {
        say(data.room, "restarting...")
        setTimeout(() => execSync("pm2 restart mpp"), 1000)
    },
    ban(data) {
        const banned = require("../banned.json")
        const _id = data.args[0]
        if (!_id) return say(data.room, "no id")
        const reason = data.args.slice(1).join(" ")
        if (!reason) return say(data.room, "no reason")

        const user = sql.prepare("SELECT * FROM users WHERE _id = ?;").get(_id)
        if (!user) return say(data.room, "no user")
        if (banned.some((b) => b === user.ip))
            return say(data.room, "already banned")

        banned.push(user.ip)

        fs.writeFileSync("./banned.json", JSON.stringify(banned))
        say(data.room, `Banned ${user.name}(${user._id}) for ${reason}`)
        for (const p of Array.from(
            data.room.server.connections.values()
        ).filter((usr) => usr.user?._id === _id)) {
            p.ws.close()
        }
    },
    unban(data) {
        const banned = require("../banned.json")
        const _id = data.args[0]

        if (!_id) return say(data.room, "no id")

        const user = sql.prepare("SELECT * FROM users WHERE _id = ?;").get(_id)
        if (!user) return say(data.room, "no user")
        if (!banned.some((b) => b === user.ip))
            return say(data.room, "not banned")

        banned.splice(banned.indexOf(user.ip), 1)

        fs.writeFileSync("./banned.json", JSON.stringify(banned))
        say(data.room, `unbanned ${user.name}(${user._id})`)
    },
    addtag(data) {
        const _id = data.args[0]
        const _tag = data.args[1]

        if (!_id) return say(data.room, "no id")
        if (!_tag) return say(data.room, "no tag")

        const usersIDs = _id.split(",")

        for (const _id of usersIDs) {
            const user = sql
                .prepare("SELECT * FROM users WHERE _id = ?;")
                .get(_id)
            if (!user) {
                say(data.room, "no user")
                continue
            }
            let tags = user.tag?.split(",")
            if (!tags) tags = []

            if (tags.some((t) => t === _tag)) {
                say(data.room, "already has tag")
                continue
            }
            tags.push(_tag)
            sql.prepare("UPDATE users SET tag = ? WHERE _id = ?;").run(
                tags.join(","),
                _id
            )
            // say(data.room, `added tag ${_tag} to ${user.name}(${user._id}), Please reload ${user.name}`);
            say(data.room, `added tag ${_tag} to ${user.name}(${user._id})`)

            for (const p of Array.from(
                data.room.server.connections.values()
            ).filter((usr) => usr.user?._id === _id)) {
                p.ws.close()
            }
        }
    },
    removetag(data) {
        const _id = data.args[0]
        const _tag = data.args[1]

        if (!_id) return say(data.room, "no id")
        if (!_tag) return say(data.room, "no tag")

        const user = sql.prepare("SELECT * FROM users WHERE _id = ?;").get(_id)
        if (!user) return say(data.room, "no user")
        let tags = user.tag?.split(",")
        if (!tags) tags = []

        if (!tags.some((t) => t === _tag))
            return say(data.room, "doesn't have tag")
        tags.splice(tags.indexOf(_tag), 1)
        sql.prepare("UPDATE users SET tag = ? WHERE _id = ?;").run(
            tags.join(","),
            _id
        )
        // say(data.room, `removed tag ${_tag} from ${user.name}(${user._id}), Please reload ${user.name}`);
        say(data.room, `removed tag ${_tag} from ${user.name}(${user._id})`)
        for (const p of Array.from(
            data.room.server.connections.values()
        ).filter((usr) => usr.user?._id === _id)) {
            p.ws.close()
        }
    },
}

const allUserCommands = {
    test(data) {
        say(data.room, "test")
        data.room.Notification(
            data.user._id,
            "Test",
            "Yeey, you have a notification!",
            "",
            3000,
            "#piano"
        )
    },
    i_love_spammer(data) {
        const tag = "SPAMMER"
        const _id = data.user._id

        const user = sql.prepare("SELECT * FROM users WHERE _id = ?;").get(_id)
        if (!user) return say(data.room, "no user")
        let tags = user.tag?.split(",")
        if (!tags) tags = []

        if (tags.some((t) => t === tag))
            return say(data.room, "already has tag")
        tags.push(tag)

        sql.prepare("UPDATE users SET tag = ? WHERE _id = ?;").run(
            tags.join(","),
            _id
        )

        say(data.room, `added tag ${tag} to ${user.name}(${user._id})`)

        for (const p of Array.from(
            data.room.server.connections.values()
        ).filter((usr) => usr.user?._id === _id)) {
            p.ws.close()
        }
    },
}

function say(room, msg) {
    const message = {}
    message.m = "a"
    message.a = msg
    message.p = {
        color: "#FF4422",
        id: "server.system",
        name: "Server",
        _id: "server.system",
        tag: ["BOT", "SERVER"],
        afk: null,
    }

    message.t = Date.now()
    room.sendArray([message])
    room.chatmsgs.push(message)
}

function bot(chat, room) {
    const message = chat.a
    const user = chat.p
    const data = {}
    data.room = room

    if (typeof message !== "string") return
    if (message.indexOf(prefix) !== 0) return

    const args = message.slice(prefix.length).trim().split(/ +/g)
    const command = args.shift().toLowerCase()

    data.args = args
    data.user = user

    if (user.tag?.some((t) => trusted.includes(t))) {
        if (!user.tag) return
        if (!Object.hasOwn(commands, command)) return
        const cmd = commands[command]
        return cmd(data)
    }

    if (Object.hasOwn(allUserCommands, command)) {
        const cmd = allUserCommands[command]
        return cmd(data)
    }
}

module.exports = bot
