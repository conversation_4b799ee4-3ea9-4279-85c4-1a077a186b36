const createKeccakHash = require("keccak")
const crypto = require("crypto")

function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min
}

function generateKey() {
    const password =
        "YCLcNpFu2gnnkyoGBYEzFsfEggmcpGUJKX5LZWF4AyFtDic4VBAAfexzAQvZ5EXzSVoGMPEf6Ukn7jFNea28KR2FmeNaB22gp24dWWvXDesND4wgB9Jat76tV58tMrvS"
    const salt = "f6AdV5B5zyrsQ7gf"
    return crypto.scryptSync(password, salt, 32)
}

function encrypt(text) {
    const key = generateKey()
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipheriv("aes-256-cbc", key, iv)
    const encryptedText = Buffer.concat([
        cipher.update(text, "utf8"),
        cipher.final(),
    ]).toString("hex")
    return { iv: iv.toString("hex"), encryptedText }
}

function decrypt(encryptedText, iv) {
    const key = generateKey()
    const decipher = crypto.createDecipheriv(
        "aes-256-cbc",
        key,
        Buffer.from(iv, "hex")
    )
    const decrypted = Buffer.concat([
        decipher.update(Buffer.from(encryptedText, "hex")),
        decipher.final(),
    ]).toString("utf8")
    return decrypted
}

function saveUser() {
    const randomIp = Array(4)
        .fill(0)
        .map(() => getRandomInt(256, 999))
        .join(".")
    const randomUuid = crypto.randomUUID()
    const pass =
        "GY9gIyxYTOR6VFDbe5XD3O23RR%brYnA^wIZ455gnBBnGfhhmoaCV*WPQLKzKAviedHD!DDaMCbw*Ln0MmCL7BvSswy8J$dx@hySkRYLhu9Sn6^onx7w@M1qq!x32Qi1"
    const _id = createKeccakHash("keccak256")
        .update(pass + randomIp)
        .digest("hex")
        .substring(0, 24)
    const token = encrypt(`${_id}:${randomUuid}`)

    console.log("Random IP: ", randomIp)
    console.log("Random UUID: ", randomUuid)
    console.log("_id: ", _id)
    console.log("Token: ", token)
    console.log("Decrypted token: ", decrypt(token.encryptedText, token.iv))
}

saveUser()

module.exports = {
    generateKey,
    encrypt,
    decrypt,
    saveUser,
}
