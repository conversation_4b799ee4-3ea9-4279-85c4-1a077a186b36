/**
 * Memory and CPU monitoring utility for detecting performance issues
 * after high-load events
 */

const logger = require('./Logger.js')

class MemoryMonitor {
    constructor() {
        this.isMonitoring = false
        this.monitoringInterval = null
        this.baselineMemory = null
        this.highLoadThreshold = 100 // MB
        this.monitoringDuration = 30000 // 30 seconds after high load
        this.checkInterval = 5000 // Check every 5 seconds
        
        // Track metrics
        this.metrics = {
            peakMemory: 0,
            currentConnections: 0,
            messageQueueSizes: [],
            gcCount: 0,
            lastGcTime: Date.now()
        }
        
        this.startBasicMonitoring()
    }
    
    startBasicMonitoring() {
        // Monitor every minute in production, every 10 seconds in development
        const interval = process.env.NODE_ENV === 'production' ? 60000 : 10000
        
        setInterval(() => {
            this.checkMemoryUsage()
        }, interval)
    }
    
    checkMemoryUsage() {
        const usage = process.memoryUsage()
        const heapUsedMB = Math.round(usage.heapUsed / 1024 / 1024 * 100) / 100
        
        // Update peak memory
        this.metrics.peakMemory = Math.max(this.metrics.peakMemory, heapUsedMB)
        
        // Check if we need to start intensive monitoring
        if (!this.isMonitoring && heapUsedMB > this.highLoadThreshold) {
            this.startIntensiveMonitoring()
        }
        
        // Log memory usage in development
        if (process.env.NODE_ENV !== 'production') {
            logger.debug(`Memory usage: ${heapUsedMB}MB (Peak: ${this.metrics.peakMemory}MB)`)
        }
        
        return {
            heapUsed: heapUsedMB,
            heapTotal: Math.round(usage.heapTotal / 1024 / 1024 * 100) / 100,
            rss: Math.round(usage.rss / 1024 / 1024 * 100) / 100,
            external: Math.round(usage.external / 1024 / 1024 * 100) / 100
        }
    }
    
    startIntensiveMonitoring() {
        if (this.isMonitoring) return
        
        this.isMonitoring = true
        this.baselineMemory = this.checkMemoryUsage()
        
        logger.info('Starting intensive memory monitoring due to high memory usage')
        
        this.monitoringInterval = setInterval(() => {
            const currentUsage = this.checkMemoryUsage()
            
            // Check for memory leaks
            if (currentUsage.heapUsed > this.baselineMemory.heapUsed * 1.5) {
                logger.warn(`Potential memory leak detected: ${currentUsage.heapUsed}MB (baseline: ${this.baselineMemory.heapUsed}MB)`)
                this.suggestGarbageCollection()
            }
            
            // Log detailed metrics
            logger.info(`Intensive monitoring - Memory: ${currentUsage.heapUsed}MB, Connections: ${this.metrics.currentConnections}`)
            
        }, this.checkInterval)
        
        // Stop intensive monitoring after duration
        setTimeout(() => {
            this.stopIntensiveMonitoring()
        }, this.monitoringDuration)
    }
    
    stopIntensiveMonitoring() {
        if (!this.isMonitoring) return
        
        this.isMonitoring = false
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval)
            this.monitoringInterval = null
        }
        
        const finalUsage = this.checkMemoryUsage()
        logger.info(`Stopped intensive monitoring - Final memory: ${finalUsage.heapUsed}MB`)
        
        // Reset baseline
        this.baselineMemory = null
    }
    
    suggestGarbageCollection() {
        // Force garbage collection if available (requires --expose-gc flag)
        if (global.gc) {
            const beforeGC = this.checkMemoryUsage()
            global.gc()
            const afterGC = this.checkMemoryUsage()
            
            this.metrics.gcCount++
            this.metrics.lastGcTime = Date.now()
            
            logger.info(`Forced GC: ${beforeGC.heapUsed}MB -> ${afterGC.heapUsed}MB (freed ${beforeGC.heapUsed - afterGC.heapUsed}MB)`)
        } else {
            logger.warn('Garbage collection not available. Start with --expose-gc flag for manual GC.')
        }
    }
    
    updateConnectionCount(count) {
        this.metrics.currentConnections = count
    }
    
    trackMessageQueue(clientId, queueSize) {
        this.metrics.messageQueueSizes.push({ clientId, queueSize, timestamp: Date.now() })
        
        // Keep only recent queue sizes (last 5 minutes)
        const fiveMinutesAgo = Date.now() - 300000
        this.metrics.messageQueueSizes = this.metrics.messageQueueSizes.filter(
            entry => entry.timestamp > fiveMinutesAgo
        )
        
        // Warn about large queues
        if (queueSize > 500) {
            logger.warn(`Large message queue detected for client ${clientId}: ${queueSize} messages`)
        }
    }
    
    getMetrics() {
        const currentUsage = this.checkMemoryUsage()
        
        return {
            ...this.metrics,
            currentMemory: currentUsage,
            isMonitoring: this.isMonitoring,
            averageQueueSize: this.metrics.messageQueueSizes.length > 0 
                ? this.metrics.messageQueueSizes.reduce((sum, entry) => sum + entry.queueSize, 0) / this.metrics.messageQueueSizes.length
                : 0
        }
    }
    
    // Method to be called when high load events occur
    onHighLoadEvent(eventType, details = {}) {
        logger.info(`High load event detected: ${eventType}`, details)
        
        if (!this.isMonitoring) {
            this.startIntensiveMonitoring()
        }
    }
}

// Create singleton instance
const memoryMonitor = new MemoryMonitor()

module.exports = memoryMonitor
