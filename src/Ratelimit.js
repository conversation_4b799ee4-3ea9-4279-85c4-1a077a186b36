const RateLimit = function (interval_ms) {
    this._interval_ms = interval_ms || 0 // (0 means no limit)
    this._after = 0
}

RateLimit.prototype.attempt = function (time) {
    const currentTime = time || Date.now()
    if (currentTime < this._after) return false
    this._after = currentTime + this._interval_ms
    return true
}

RateLimit.prototype.setInterval = function (interval_ms) {
    this._after += interval_ms - this._interval_ms
    this._interval_ms = interval_ms
}

const RateLimitChain = function (num, interval_ms) {
    this.setNumAndInterval(num, interval_ms)
}

RateLimitChain.prototype.attempt = function (time) {
    const currentTime = time || Date.now()
    for (let i = 0; i < this._chain.length; i++) {
        if (this._chain[i].attempt(currentTime)) return true
    }
    return false
}

RateLimitChain.prototype.setNumAndInterval = function (num, interval_ms) {
    this._chain = []
    for (let i = 0; i < num; i++) {
        this._chain.push(new RateLimit(interval_ms))
    }
}

exports.RateLimit = RateLimit
exports.RateLimitChain = RateLimitChain
