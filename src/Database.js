const fs = require("node:fs")
const path = "./src/db"
const dbFile = `${path}/users.sqlite`

if (!fs.existsSync(path)) fs.mkdirSync(path)

const sqlite3 = require("better-sqlite3")
global.sql = new sqlite3(dbFile)

const tableExists = sql
    .prepare(
        "SELECT count(*) FROM sqlite_master WHERE type='table' AND name = 'users';"
    )
    .get()["count(*)"]

if (!tableExists) {
    sql.exec(`
        CREATE TABLE users (
            _id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            color TEXT NOT NULL,
            ip TEXT NOT NULL,
            tag TEXT
        );
        CREATE UNIQUE INDEX idx_users_id ON users (_id);
        PRAGMA synchronous = 1;
        PRAGMA journal_mode = wal;
    `)
}

const database = {
    getUser(_id) {
        return sql.prepare("SELECT * FROM users WHERE _id = ?;").get(_id)
    },
    getUsers() {
        return sql.prepare("SELECT * FROM users;").all()
    },
    setUser(user) {
        sql.prepare(
            "INSERT OR REPLACE INTO users (_id, name, color, ip, tag) VALUES (@_id, @name, @color, @ip, @tag);"
        ).run(user)
    },
}

module.exports = database
