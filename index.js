require("dotenv").config()

global.WebSocket = require("ws")
global.EventEmitter = require("events").EventEmitter
global.fs = require("node:fs")
global.createKeccakHash = require("keccak")
global.express = require("express")
global.http = require("node:http")

global.database = require("./src/Database.js")

global.isString = (a) => typeof a === "string"
global.isBool = (a) => typeof a === "boolean"
global.isObj = (a) => typeof a === "object" && !Array.isArray(a) && a !== null

const Server = require("./src/Server.js")
const config = require("./config")
global.SERVER = new Server(config)

console.log("-".repeat(64))
console.log(`Set default room color to: ${config.defaultRoomColor}`)
console.log(`Set default room color2 to: ${config.defaultRoomColor2}`)
console.log(`Listening on 0.0.0.0:${config.port}`)
console.log(`Password is: ${config.adminpass}`)
console.log("-".repeat(64))
